<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android" package="puzzle.brick.blaster.sand" xmlns:tools="http://schemas.android.com/tools" android:installLocation="preferExternal" android:versionCode="7" android:versionName="0.1">
  <supports-screens android:smallScreens="true" android:normalScreens="true" android:largeScreens="true" android:xlargeScreens="true" android:anyDensity="true" />
  <application android:appCategory="game" android:appComponentFactory="androidx.core.app.CoreComponentFactory" android:extractNativeLibs="true" android:name="androidx.multidex.MultiDexApplication" android:icon="@drawable/app_icon" android:label="@string/app_name" android:networkSecurityConfig="@xml/network_security_config">
    <activity android:name="com.unity3d.player.UnityPlayerGameActivity" android:theme="@style/BaseUnityGameActivityTheme" android:label="@string/app_name" android:exported="true" android:enabled="true">
      <intent-filter>
        <action android:name="android.intent.action.MAIN" />
        <category android:name="android.intent.category.LAUNCHER" />
      </intent-filter>
      <meta-data android:name="unityplayer.UnityActivity" android:value="true" />
      <meta-data android:name="unityplayer.SkipPermissionsDialog" android:value="true" />
      <meta-data android:name="notch_support" android:value="true" />
    </activity>
    <meta-data android:name="unity.render-outside-safearea" android:value="true" />
    <meta-data android:name="google_analytics_default_allow_analytics_storage" android:value="true" />
    <meta-data android:name="google_analytics_default_allow_ad_storage" android:value="true" />
    <meta-data android:name="google_analytics_default_allow_ad_user_data" android:value="true" />
    <meta-data android:name="google_analytics_default_allow_ad_personalization_signals" android:value="true" />
  </application>
  <uses-permission android:name="android.permission.INTERNET" />
  <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
  <uses-permission android:name="android.permission.ACCESS_WIFI_STATE" />
  <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
  <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
  <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
  <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
  <uses-permission android:name="com.android.vending.BILLING" />
</manifest>