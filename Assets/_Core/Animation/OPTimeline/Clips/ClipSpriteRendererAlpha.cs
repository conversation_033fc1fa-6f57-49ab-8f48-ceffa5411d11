using UnityEngine;

namespace OnePuz.OPTimeline
{
    [System.Serializable]
    [OPClipCreate("SpriteRenderer/Alpha", "SpriteRenderer Alpha")]
    public class ClipSpriteRendererAlpha : OPTweenClip<float,SpriteRenderer>
    {
        protected override float GetCurrentValue()
        {
            return target.color.a;
        }

        protected override void SetValue(float newValue)
        {
            var color = target.color;
            color.a = newValue;
            target.color = color;
        }
        
        protected override void OnUpdate(OPEvaluateState state, float timelineTime, float clipTime,
            float normalizedClipTime, bool previewMode)
        {
            if(state != OPEvaluateState.Running) return;
            SetValue(Mathf.LerpUnclamped(CurrentFrom, to, ease.Lerp(normalizedClipTime)));
        }
    }
}