using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using OnePuz.Editor;
using OnePuz.Shared;
using UnityEditor;
using UnityEditor.UIElements;
using UnityEngine;
using UnityEngine.UIElements;

namespace OnePuz.OPTimeline.Editor
{
    [CustomEditor(typeof(OPAnimator), true)]
    public class OPAnimatorEditor : OMBaseEditor
    {
        public static List<OPClip> AllClipsInProject { get; private set; }

        public static string CopiedClipJson { get; set; }
        public static Type CopiedClipType { get; set; }
        public static OPAnimatorEditor PreviewInstance { get; private set; }

        public OPAnimator Animator { get; private set; }
        public OPTimeline Timeline { get; private set; }
        public OPClip SelectedClip { get; private set; }

        private List<IOPUpdatable> _updateables = new List<IOPUpdatable>();

        private void OnEnable()
        {
            Animator = target as OPAnimator;
            Animator.TimelineTime = 0;
            if (AllClipsInProject == null || AllClipsInProject.Count == 0) AllClipsInProject = GetAllClips();
            SetSelectedClip(null);
            EditorApplication.update += Update;
            Undo.undoRedoPerformed += UndoRedoPerformed;
            EditorApplication.playModeStateChanged += OnplayModeStateChanged;
            EditorApplication.quitting += OnEditorApplicationQuitting;

            Update();
        }

        private void OnDisable()
        {
            SetPreviewInstance(null);
            EditorApplication.update -= Update;
            Undo.undoRedoPerformed -= UndoRedoPerformed;
            EditorApplication.playModeStateChanged -= OnplayModeStateChanged;
            EditorApplication.quitting -= OnEditorApplicationQuitting;
        }

        /// <summary>
        /// On Undo Redo Performed
        /// </summary>
        private void UndoRedoPerformed()
        {
            Timeline.ContentSection.DestroyAndInstantiateClips();
        }

        /// <summary>
        /// On Play Mode State Changed
        /// </summary>
        /// <param name="playModeState"></param>
        private void OnplayModeStateChanged(PlayModeStateChange playModeState)
        {
            if (playModeState == PlayModeStateChange.ExitingEditMode)
            {
                if (PreviewInstance != null)
                {
                    SetPreviewInstance(null);
                }
            }
        }

        /// <summary>
        /// On the Editor Application Quitting
        /// </summary>
        private void OnEditorApplicationQuitting()
        {
            SetPreviewInstance(null);
        }

        /// <summary>
        /// On Editor Update 
        /// </summary>
        private void Update()
        {
            foreach (var updateable in _updateables)
            {
                updateable.Update();
            }
        }

        /// <summary>
        /// Draw The inspector
        /// </summary>
        /// <param name="root"></param>
        protected override void DrawInspector(VisualElement root)
        {
            base.DrawInspector(root);
            root.styleSheets.Add(Resources.Load<StyleSheet>("OPAnimator"));
            DrawFields();
            DrawTimeline();
            DrawInspector();
            DrawButtonsSection();
        }

        /// <summary>
        /// Draw All the Fields of the OPAnimator
        /// </summary>
        private void DrawFields()
        {
            var fullDurationProp = serializedObject.FindProperty("fullDuration");
            if (fullDurationProp != null) Root.Add(new PropertyField(fullDurationProp));

            var speedProp = serializedObject.FindProperty("speed");
            if (speedProp != null) Root.Add(new PropertyField(speedProp));

            var timeIndependentProp = serializedObject.FindProperty("timeIndependent");
            if (timeIndependentProp != null) Root.Add(new PropertyField(timeIndependentProp));

            var playOnEnableProp = serializedObject.FindProperty("playOnEnable");
            if (playOnEnableProp != null) Root.Add(new PropertyField(playOnEnableProp));

            var loopProp = serializedObject.FindProperty("loop");
            if (loopProp != null) Root.Add(new PropertyField(loopProp));

            var eventsProp = serializedObject.FindProperty("events");
            if (eventsProp != null) Root.Add(new PropertyField(eventsProp));

        }

        /// <summary>
        /// Draw the Timeline
        /// </summary>
        private void DrawTimeline()
        {
            Timeline = new OPTimeline(Animator, this);
            _updateables.Add(Timeline);
            Root.Add(Timeline);
        }

        /// <summary>
        /// Draw the Clip Inspector
        /// </summary>
        private void DrawInspector()
        {
            var container = new VisualElement();
            container.AddToClassList("inspector");
            Root.Add(container);

            var header = new Label("Inspector");
            container.Add(header);

            container.Add(new IMGUIContainer(() =>
            {
                if (GetSelectedClip() == null)
                {
                    return;
                }

                serializedObject.Update();
                var indexOf = Animator.GetClips().IndexOf(GetSelectedClip());
                if (indexOf < 0)
                {
                    return;
                }
                var clipProp = serializedObject.FindProperty("clips").GetArrayElementAtIndex(indexOf);
                var monoScript = FindClass(GetSelectedClip().GetType().Name);
                if (monoScript != null)
                {
                    GUI.enabled = false;
                    EditorGUILayout.ObjectField("Script", monoScript, typeof(MonoScript), false);
                    GUI.enabled = true;
                }

                DrawAllProperties(clipProp);

                var methodInfos = GetSelectedClip().GetType().GetMethods(BindingFlags.Default | BindingFlags.Instance | BindingFlags.NonPublic | BindingFlags.Public);
                foreach (var methodInfo in methodInfos)
                {
                    var customButtonAttribute = methodInfo.GetCustomAttribute<OMCustomButtonAttribute>();
                    if (customButtonAttribute != null)
                    {
                        if (GUILayout.Button(customButtonAttribute.ButtonName))
                        {
                            methodInfo.Invoke(GetSelectedClip(), null);
                        }
                    }
                }

                serializedObject.ApplyModifiedProperties();
            }));
        }

        /// <summary>
        /// Draw the Buttons Section
        /// </summary>
        private void DrawButtonsSection()
        {
            var gui = new IMGUIContainer();
            gui.style.marginTop = 10;
            gui.style.paddingRight = 5;
            gui.style.paddingLeft = 5;
            Root.Add(gui);

            gui.onGUIHandler = () =>
            {
                var animatorPlayer = target as OPAnimatorPlayer;
                if (animatorPlayer == null) return;
                GUI.enabled = Application.isPlaying;

                if (animatorPlayer.PlayState == OPAnimatorPlayState.Paused)
                {
                    if (GUILayout.Button("Resume"))
                    {
                        animatorPlayer.Resume();
                    }
                }
                else
                {
                    if (animatorPlayer.PlayState == OPAnimatorPlayState.Playing)
                    {
                        if (GUILayout.Button("Pause"))
                        {
                            animatorPlayer.Pause();
                        }
                    }
                    else
                    {
                        if (GUILayout.Button("Play"))
                        {
                            animatorPlayer.Play();
                        }
                    }
                }

                if (GUILayout.Button("Restart"))
                {
                    animatorPlayer.Restart();
                }


                GUI.enabled = true;
            };
        }

        public override bool RequiresConstantRepaint()
        {
            // TODO: Original code is TRUE, don't know if it's necessary
            return false;
        }

        /// <summary>
        /// Set the selected clip
        /// </summary>
        /// <param name="clip"></param>
        public void SetSelectedClip(OPClip clip)
        {
            SelectedClip = clip;
        }

        /// <summary>
        /// Get the selected clip
        /// </summary>
        /// <returns></returns>
        public OPClip GetSelectedClip()
        {
            return SelectedClip;
        }

        /// <summary>
        /// Draw all the properties of the SerializedProperty
        /// </summary>
        /// <param name="property"></param>
        /// <param name="enterChildren"></param>
        private void DrawAllProperties(SerializedProperty property, bool enterChildren = true)
        {
            if (property == null)
            {
                EditorGUILayout.HelpBox("SerializedProperty is null", MessageType.Error);
                return;
            }

            var currentProperty = property.Copy();
            var startDepth = currentProperty.depth;
            EditorGUIUtility.labelWidth = 100;
            var currentClipTypeName = GetSelectedClip().GetType().Name;
            while (currentProperty.NextVisible(enterChildren) && currentProperty.depth > startDepth)
            {
                enterChildren = false;
                if (currentProperty.name == "m_Script")
                {
                    continue;
                }

                if (currentProperty.name == "fromType")
                {
                    continue;
                }

                if (currentProperty.name == "from")
                {
                    var fromTypeProp = property.FindPropertyRelative("fromType");
                    EditorGUILayout.PropertyField(fromTypeProp);

                    if (fromTypeProp.enumValueIndex == 0)
                    {
                        DrawFromToValueProperties(currentProperty, "ApplyValueFrom");
                    }
                    else
                    {
                        GUI.enabled = false;
                        DrawFromToValueProperties(currentProperty, "ApplyValueFrom");
                        GUI.enabled = true;
                    }

                    continue;
                }

                if (currentProperty.name == "to")
                {
                    DrawFromToValueProperties(currentProperty, "ApplyValueTo");
                    continue;
                }

                EditorGUILayout.PropertyField(currentProperty, true);
            }
        }

        private void DrawFromToValueProperties(SerializedProperty property, string methodName)
        {
            var wideMode = EditorGUIUtility.wideMode;
            EditorGUIUtility.wideMode = true;

            EditorGUILayout.BeginHorizontal();
            EditorGUILayout.PropertyField(property, true);
            // Draw button to copy the value
            if (GUILayout.Button("Apply", GUILayout.Width(50)))
            {
                var clip = SelectedClip;
                if (clip != null)
                {
                    // Get the type of the clip
                    var clipType = clip.GetType();
                    // Get the base type (OPClipCore<>) of the clip
                    var baseType = clipType.BaseType;
                    if (baseType != null)
                    {
                        // Get the MethodInfo for Method
                        var methodInfo = baseType.GetMethod(methodName);
                        if (methodInfo != null)
                        {
                            // Invoke the Method method
                            methodInfo.Invoke(clip, null);
                        }
                        else
                        {
                            Debug.LogError($"{methodName} method not found in OPClipCore");
                        }
                    }
                    else
                    {
                        Debug.LogError("Base type of clip is null");
                    }
                }
                else
                {
                    Debug.LogError("SelectedClip is null");
                }
            }
            EditorGUILayout.EndHorizontal();

            EditorGUIUtility.wideMode = wideMode;
        }

        /// <summary>
        /// Find the class MonoScript by name
        /// </summary>
        /// <param name="className"></param>
        /// <returns></returns>
        static MonoScript FindClass(string className)
        {
            string[] assetsPaths = AssetDatabase.FindAssets(className);

            foreach (string assetPath in assetsPaths)
            {
                string assetFilePath = AssetDatabase.GUIDToAssetPath(assetPath);

                if (assetFilePath.EndsWith(className + ".cs")) // Assuming it's a C# file
                {
                    var loadAssetAtPath = AssetDatabase.LoadAssetAtPath<MonoScript>(assetFilePath);
                    return loadAssetAtPath;
                }
            }
            return null;
        }

        /// <summary>
        /// Get All clips in the project
        /// </summary>
        /// <returns></returns>
        private static List<OPClip> GetAllClips()
        {
            var types = (from domainAssembly in AppDomain.CurrentDomain.GetAssemblies()
                         from assemblyType in domainAssembly.GetTypes()
                         where (assemblyType.IsSubclassOf(typeof(OPClip)) && !assemblyType.IsAbstract)
                         select assemblyType);

            var uicClips = new List<OPClip>();
            foreach (var type in types)
            {
                uicClips.Add((OPClip)Activator.CreateInstance(type));
            }
            return uicClips;
        }

        /// <summary>
        /// Copy the Clip
        /// </summary>
        /// <param name="clip"></param>
        public void CopyClip(OPClip clip)
        {
            if (clip == null) return;
            CopiedClipJson = JsonUtility.ToJson(clip);
            CopiedClipType = clip.GetType();
        }

        /// <summary>
        /// Get the Copied Clip and reset the stored clip
        /// </summary>
        /// <param name="result"></param>
        /// <returns></returns>
        public bool TryGetClipFromCopy(out OPClip result)
        {
            try
            {
                var clip = JsonUtility.FromJson(CopiedClipJson, CopiedClipType);
                result = clip as OPClip;
                CopiedClipJson = "";
                CopiedClipType = null;
                return result != null;
            }
            catch (Exception)
            {
                result = null;
                return false;
            }
        }

        /// <summary>
        /// Set if this instance is the preview instance
        /// </summary>
        /// <param name="instance"></param>
        public void SetPreviewInstance(OPAnimatorEditor instance)
        {
            if (PreviewInstance != null)
            {
                foreach (var clip in PreviewInstance.Animator.GetClips())
                {
                    clip.OnPreviewModeChanged(false);
                }
            }
            PreviewInstance = instance;
            if (PreviewInstance != null)
            {
                foreach (var clip in PreviewInstance.Animator.GetClips())
                {
                    clip.OnPreviewModeChanged(true);
                }
            }
        }

        /// <summary>
        /// returns if this instance is the preview instance
        /// </summary>
        /// <returns></returns>
        public bool IsPreviewInstance()
        {
            return PreviewInstance == this;
        }
    }
}