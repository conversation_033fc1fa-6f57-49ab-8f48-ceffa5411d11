using System.Collections.Generic;
using Unity.Collections;
using Unity.Jobs;
using UnityEngine;

namespace OP.BlockSand.Bomb
{
    /// <summary>
    /// Optimization utilities for bomb system performance
    /// </summary>
    public static class BombSystemOptimizer
    {
        // Cache for frequently used calculations
        private static readonly Dictionary<int, List<Vector2Int>> _radiusPixelCache = new();
        private static readonly Dictionary<Vector2Int, float> _distanceCache = new();
        
        // Object pools
        private static readonly Queue<List<Vector2Int>> _pixelListPool = new();
        private static readonly Queue<List<BombPixelAnimationData>> _animationDataPool = new();
        
        /// <summary>
        /// Get pixels within radius using cached calculations
        /// </summary>
        public static List<Vector2Int> GetPixelsInRadius(Vector2Int center, int radiusInPixels)
        {
            var cacheKey = radiusInPixels;
            
            if (!_radiusPixelCache.TryGetValue(cacheKey, out var cachedOffsets))
            {
                cachedOffsets = CalculateRadiusOffsets(radiusInPixels);
                _radiusPixelCache[cacheKey] = cachedOffsets;
            }
            
            var result = GetPooledPixelList();
            result.Clear();
            
            foreach (var offset in cachedOffsets)
            {
                result.Add(center + offset);
            }
            
            return result;
        }
        
        /// <summary>
        /// Calculate pixel offsets for a given radius (cached)
        /// </summary>
        private static List<Vector2Int> CalculateRadiusOffsets(int radius)
        {
            var offsets = new List<Vector2Int>();
            
            for (var y = -radius; y <= radius; y++)
            {
                for (var x = -radius; x <= radius; x++)
                {
                    var distance = Mathf.Sqrt(x * x + y * y);
                    if (distance <= radius)
                    {
                        offsets.Add(new Vector2Int(x, y));
                    }
                }
            }
            
            return offsets;
        }
        
        /// <summary>
        /// Get cached distance between two points
        /// </summary>
        public static float GetCachedDistance(Vector2Int from, Vector2Int to)
        {
            var key = new Vector2Int(
                Mathf.Abs(to.x - from.x),
                Mathf.Abs(to.y - from.y)
            );
            
            if (!_distanceCache.TryGetValue(key, out var distance))
            {
                distance = Vector2.Distance(from, to);
                _distanceCache[key] = distance;
            }
            
            return distance;
        }
        
        /// <summary>
        /// Get a pooled pixel list to avoid allocations
        /// </summary>
        public static List<Vector2Int> GetPooledPixelList()
        {
            if (_pixelListPool.Count > 0)
            {
                return _pixelListPool.Dequeue();
            }
            
            return new List<Vector2Int>();
        }
        
        /// <summary>
        /// Return a pixel list to the pool
        /// </summary>
        public static void ReturnPooledPixelList(List<Vector2Int> list)
        {
            if (list == null) return;
            
            list.Clear();
            _pixelListPool.Enqueue(list);
        }
        
        /// <summary>
        /// Get a pooled animation data list
        /// </summary>
        public static List<BombPixelAnimationData> GetPooledAnimationDataList()
        {
            if (_animationDataPool.Count > 0)
            {
                return _animationDataPool.Dequeue();
            }
            
            return new List<BombPixelAnimationData>();
        }
        
        /// <summary>
        /// Return an animation data list to the pool
        /// </summary>
        public static void ReturnPooledAnimationDataList(List<BombPixelAnimationData> list)
        {
            if (list == null) return;
            
            list.Clear();
            _animationDataPool.Enqueue(list);
        }
        
        /// <summary>
        /// Optimized flood fill job with better memory management
        /// </summary>
        public static JobHandle ScheduleOptimizedFloodFill(
            NativeArray<Pixel> pixelBuffer,
            int worldWidth,
            int worldHeight,
            Vector2Int seedPixel,
            int targetMaterialIndex,
            NativeList<Vector2Int> results)
        {
            var job = new OptimizedBombFloodFillJob
            {
                pixelBuffer = pixelBuffer,
                worldWidth = worldWidth,
                worldHeight = worldHeight,
                seedPixel = seedPixel,
                targetMaterialIndex = targetMaterialIndex,
                results = results
            };
            
            return job.Schedule();
        }
        
        /// <summary>
        /// Clear all caches to free memory
        /// </summary>
        public static void ClearCaches()
        {
            _radiusPixelCache.Clear();
            _distanceCache.Clear();
            
            // Clear pools
            _pixelListPool.Clear();
            _animationDataPool.Clear();
            
            OLogger.Log("BombSystemOptimizer: Caches cleared");
        }
        
        /// <summary>
        /// Get cache statistics for debugging
        /// </summary>
        public static string GetCacheStats()
        {
            return $"Radius Cache: {_radiusPixelCache.Count} entries, " +
                   $"Distance Cache: {_distanceCache.Count} entries, " +
                   $"Pixel Pool: {_pixelListPool.Count} available, " +
                   $"Animation Pool: {_animationDataPool.Count} available";
        }
    }
    
    /// <summary>
    /// Optimized version of bomb flood fill job with better performance
    /// </summary>
    [Unity.Burst.BurstCompile]
    public struct OptimizedBombFloodFillJob : IJob
    {
        [ReadOnly] public NativeArray<Pixel> pixelBuffer;
        [ReadOnly] public int worldWidth;
        [ReadOnly] public int worldHeight;
        [ReadOnly] public Vector2Int seedPixel;
        [ReadOnly] public int targetMaterialIndex;
        
        public NativeList<Vector2Int> results;
        
        public void Execute()
        {
            results.Clear();
            
            // Early exit if seed pixel is invalid
            if (!IsValidCoordinate(seedPixel.x, seedPixel.y))
                return;
            
            var seedIndex = GetIndex(seedPixel.x, seedPixel.y);
            var seedPixelData = pixelBuffer[seedIndex];
            
            if (seedPixelData.IsEmpty() || seedPixelData.materialIndex != targetMaterialIndex)
                return;
            
            // Use more efficient flood fill with stack
            var visited = new NativeArray<bool>(pixelBuffer.Length, Allocator.Temp);
            var stack = new NativeArray<Vector2Int>(1024, Allocator.Temp); // Fixed size stack
            var stackTop = 0;
            
            // Add seed to stack
            stack[stackTop] = seedPixel;
            stackTop++;
            
            while (stackTop > 0 && stackTop < stack.Length)
            {
                // Pop from stack
                stackTop--;
                var current = stack[stackTop];
                var index = GetIndex(current.x, current.y);
                
                // Skip if already visited or invalid
                if (visited[index] || pixelBuffer[index].IsEmpty() ||
                    pixelBuffer[index].materialIndex != targetMaterialIndex)
                    continue;
                
                // Mark as visited and add to results
                visited[index] = true;
                results.Add(current);
                
                // Add neighbors to stack (4-directional)
                TryAddToStack(current.x - 1, current.y, stack, ref stackTop, visited);
                TryAddToStack(current.x + 1, current.y, stack, ref stackTop, visited);
                TryAddToStack(current.x, current.y - 1, stack, ref stackTop, visited);
                TryAddToStack(current.x, current.y + 1, stack, ref stackTop, visited);
            }
            
            visited.Dispose();
            stack.Dispose();
        }
        
        private void TryAddToStack(int x, int y, NativeArray<Vector2Int> stack, ref int stackTop, NativeArray<bool> visited)
        {
            if (!IsValidCoordinate(x, y) || stackTop >= stack.Length - 1)
                return;
            
            var index = GetIndex(x, y);
            if (visited[index])
                return;
            
            stack[stackTop] = new Vector2Int(x, y);
            stackTop++;
        }
        
        private bool IsValidCoordinate(int x, int y)
        {
            return x >= 0 && x < worldWidth && y >= 0 && y < worldHeight;
        }
        
        private int GetIndex(int x, int y)
        {
            return y * worldWidth + x;
        }
    }
}
