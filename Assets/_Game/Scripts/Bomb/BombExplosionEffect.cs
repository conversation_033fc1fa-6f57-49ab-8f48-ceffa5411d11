using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Cysharp.Threading.Tasks;

namespace OP.BlockSand.Bomb
{
    /// <summary>
    /// Visual effect system for bomb explosion - creates expanding wave effect and clears pixels
    /// </summary>
    public class BombExplosionEffect : MonoBehaviour
    {
        [Header("Explosion Settings")]
        [SerializeField] private float _explosionDuration = 1.5f; // Total duration of explosion effect
        [SerializeField] private float _waveSpeed = 8f; // Speed of explosion wave
        [SerializeField] private int _waveRings = 3; // Number of wave rings
        [SerializeField] private float _ringDelay = 0.1f; // Delay between rings
        
        [Header("Visual Effects")]
        [SerializeField] private Color32 _explosionColor = new Color32(255, 200, 100, 255); // Orange explosion color
        [SerializeField] private Color32 _waveColor = new Color32(255, 255, 255, 200); // White wave color
        [SerializeField] private AnimationCurve _intensityCurve = AnimationCurve.EaseInOut(0f, 1f, 1f, 0f);
        
        [Header("Particle Effects")]
        [SerializeField] private ParticleSystem _explosionParticles;
        [SerializeField] private ParticleSystem _dustParticles;
        
        private PixelWorld _pixelWorld;
        private PixelMaterials _materialDefinition;
        private readonly List<BombExplosionPixelData> _explosionPixels = new();
        private Coroutine _explosionCoroutine;
        
        public bool IsExploding { get; private set; } = false;
        
        public void Initialize(PixelWorld pixelWorld, PixelMaterials materialDefinition)
        {
            _pixelWorld = pixelWorld;
            _materialDefinition = materialDefinition;
        }
        
        /// <summary>
        /// Start bomb explosion effect at specified position
        /// </summary>
        public async UniTask StartExplosionEffect(Vector3 worldPosition, List<Vector2Int> affectedPixels, System.Action onComplete = null)
        {
            if (IsExploding)
            {
                StopExplosion();
            }
            
            // Set position
            transform.position = worldPosition;
            
            // Prepare explosion data
            PrepareExplosionData(worldPosition, affectedPixels);
            
            // Start explosion sequence
            _explosionCoroutine = StartCoroutine(ExplosionSequence());
            
            // Wait for explosion to complete
            await UniTask.WaitUntil(() => !IsExploding);
            
            onComplete?.Invoke();
        }
        
        /// <summary>
        /// Stop current explosion effect
        /// </summary>
        public void StopExplosion()
        {
            if (_explosionCoroutine != null)
            {
                StopCoroutine(_explosionCoroutine);
                _explosionCoroutine = null;
            }
            
            IsExploding = false;
            _explosionPixels.Clear();
            
            // Stop particle effects
            if (_explosionParticles != null && _explosionParticles.isPlaying)
            {
                _explosionParticles.Stop();
            }
            
            if (_dustParticles != null && _dustParticles.isPlaying)
            {
                _dustParticles.Stop();
            }
        }
        
        private void PrepareExplosionData(Vector3 centerWorldPos, List<Vector2Int> affectedPixels)
        {
            _explosionPixels.Clear();
            
            // Convert world position to pixel position for distance calculations
            var centerPixelPos = WorldToPixelPosition(centerWorldPos);
            
            foreach (var pixelPos in affectedPixels)
            {
                if (!_pixelWorld.TryGetPixelAt(pixelPos.x, pixelPos.y, out var pixel)) continue;
                
                var distance = Vector2.Distance(pixelPos, centerPixelPos);
                var explosionData = new BombExplosionPixelData(
                    pixelPos,
                    new Color32(pixel.r, pixel.g, pixel.b, pixel.a),
                    GetMaterialIdFromIndex(pixel.materialIndex),
                    distance
                );
                _explosionPixels.Add(explosionData);
            }
            
            // Sort by distance for wave effect
            _explosionPixels.Sort((a, b) => a.distanceFromCenter.CompareTo(b.distanceFromCenter));
        }
        
        private IEnumerator ExplosionSequence()
        {
            IsExploding = true;
            
            // Start particle effects
            StartParticleEffects();
            
            // Create multiple wave rings
            var ringCoroutines = new List<Coroutine>();
            for (var i = 0; i < _waveRings; i++)
            {
                var delay = i * _ringDelay;
                ringCoroutines.Add(StartCoroutine(CreateWaveRing(delay)));
            }
            
            // Wait for all rings to complete
            foreach (var ringCoroutine in ringCoroutines)
            {
                yield return ringCoroutine;
            }
            
            // Final cleanup - ensure all pixels are cleared
            ClearAllExplosionPixels();
            
            IsExploding = false;
        }
        
        private IEnumerator CreateWaveRing(float startDelay)
        {
            yield return new WaitForSeconds(startDelay);
            
            var maxDistance = GetMaxDistance();
            var waveRadius = 0f;
            var elapsedTime = 0f;
            
            while (elapsedTime < _explosionDuration && IsExploding)
            {
                waveRadius = (elapsedTime / _explosionDuration) * (maxDistance + 2f);
                var intensity = _intensityCurve.Evaluate(elapsedTime / _explosionDuration);
                
                // Update pixels in current wave
                UpdateWavePixels(waveRadius, intensity);
                
                elapsedTime += Time.deltaTime;
                yield return null;
            }
        }
        
        private void UpdateWavePixels(float waveRadius, float intensity)
        {
            foreach (var explosionData in _explosionPixels)
            {
                var distanceFromWave = Mathf.Abs(explosionData.distanceFromCenter - waveRadius);
                
                // Only affect pixels close to the wave front
                if (distanceFromWave <= 2f)
                {
                    var waveIntensity = (2f - distanceFromWave) / 2f * intensity;
                    var color = GetExplosionColor(explosionData.originalColor, waveIntensity);
                    
                    _pixelWorld.DrawPixelForAnimation(explosionData.position.x, explosionData.position.y,
                        color.r, color.g, color.b, color.a, explosionData.materialId);
                }
                else if (waveRadius > explosionData.distanceFromCenter + 2f)
                {
                    // Wave has passed - clear the pixel
                    _pixelWorld.DrawPixelForAnimation(explosionData.position.x, explosionData.position.y,
                        0, 0, 0, 0, PixelMaterialId.Empty);
                }
            }
        }
        
        private Color32 GetExplosionColor(Color32 originalColor, float intensity)
        {
            // Blend between original color and explosion color based on intensity
            if (intensity < 0.3f)
            {
                // Original to brighter
                var t = intensity / 0.3f;
                return Color32.Lerp(originalColor, GetBrighterColor(originalColor), t);
            }
            else if (intensity < 0.7f)
            {
                // Brighter to explosion color
                var t = (intensity - 0.3f) / 0.4f;
                return Color32.Lerp(GetBrighterColor(originalColor), _explosionColor, t);
            }
            else
            {
                // Explosion color to wave color
                var t = (intensity - 0.7f) / 0.3f;
                return Color32.Lerp(_explosionColor, _waveColor, t);
            }
        }
        
        private Color32 GetBrighterColor(Color32 originalColor)
        {
            var brighterR = Mathf.Min(255, originalColor.r + 80);
            var brighterG = Mathf.Min(255, originalColor.g + 80);
            var brighterB = Mathf.Min(255, originalColor.b + 80);
            return new Color32((byte)brighterR, (byte)brighterG, (byte)brighterB, originalColor.a);
        }
        
        private void StartParticleEffects()
        {
            if (_explosionParticles != null)
            {
                _explosionParticles.Play();
            }
            
            if (_dustParticles != null)
            {
                _dustParticles.Play();
            }
        }
        
        private void ClearAllExplosionPixels()
        {
            foreach (var explosionData in _explosionPixels)
            {
                _pixelWorld.DrawPixelForAnimation(explosionData.position.x, explosionData.position.y,
                    0, 0, 0, 0, PixelMaterialId.Empty);
            }
        }
        
        private float GetMaxDistance()
        {
            var maxDistance = 0f;
            foreach (var explosionData in _explosionPixels)
            {
                if (explosionData.distanceFromCenter > maxDistance)
                {
                    maxDistance = explosionData.distanceFromCenter;
                }
            }
            return maxDistance;
        }
        
        private Vector2 WorldToPixelPosition(Vector3 worldPosition)
        {
            // Simple conversion - may need adjustment based on actual coordinate system
            return new Vector2(worldPosition.x * _pixelWorld.PixelsPerUnit, worldPosition.y * _pixelWorld.PixelsPerUnit);
        }
        
        private PixelMaterialId GetMaterialIdFromIndex(int materialIndex)
        {
            if (materialIndex >= 0 && materialIndex < _materialDefinition.Materials.Length)
                return _materialDefinition.Materials[materialIndex].id;
            
            OLogger.LogError($"Invalid material index {materialIndex}!");
            return PixelMaterialId.Empty;
        }
    }
    
    /// <summary>
    /// Data structure for bomb explosion pixel data
    /// </summary>
    public struct BombExplosionPixelData
    {
        public Vector2Int position;
        public Color32 originalColor;
        public PixelMaterialId materialId;
        public float distanceFromCenter;
        
        public BombExplosionPixelData(Vector2Int pos, Color32 color, PixelMaterialId matId, float distance)
        {
            position = pos;
            originalColor = color;
            materialId = matId;
            distanceFromCenter = distance;
        }
    }
}
