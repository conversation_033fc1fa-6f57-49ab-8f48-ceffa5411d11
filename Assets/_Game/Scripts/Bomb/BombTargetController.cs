using System;
using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using OP.BlockSand.Shape;
using UnityEngine;
using PrimeTween;

namespace OP.BlockSand.Bomb
{
    /// <summary>
    /// Controller for bomb target icon that can be dragged around the board
    /// </summary>
    public class BombTargetController : DraggableShapeBase
    {
        [SerializeField]
        private SpriteRenderer _targetIcon;
        
        [SerializeField]
        private float _bombRadius = 2f; // Radius in world units for bomb effect
        
        private Vector2Int _currentPixelPosition;
        private List<Vector2Int> _affectedPixels = new List<Vector2Int>();
        private Board _board;
        private bool _isActive = false;
        
        public override string Id { get; protected set; } = "BombTarget";
        public override Rect Rect => new Rect(-0.5f, -0.5f, 1f, 1f); // 1x1 unit rect for target icon
        
        public Vector2Int CurrentPixelPosition => _currentPixelPosition;
        public List<Vector2Int> AffectedPixels => _affectedPixels;
        public float BombRadius => _bombRadius;
        public bool IsActive => _isActive;
        
        // Events
        public event Action<Vector2Int, List<Vector2Int>> OnTargetPositionChanged;
        public event Action OnTargetActivated;
        public event Action OnTargetDeactivated;
        
        public void Initialize(Board board)
        {
            _board = board;
            _isActive = false;
            
            // Hide initially
            gameObject.SetActive(false);
        }
        
        public void ActivateTarget(Vector3 initialWorldPosition)
        {
            if (_isActive) return;
            
            _isActive = true;
            gameObject.SetActive(true);
            
            // Set initial position
            transform.position = initialWorldPosition;
            _originalPosition = initialWorldPosition;
            
            // Update pixel position and affected pixels
            UpdateTargetPosition();
            
            // Show with animation
            Show();
            
            OnTargetActivated?.Invoke();
        }
        
        public void DeactivateTarget()
        {
            if (!_isActive) return;
            
            _isActive = false;
            _affectedPixels.Clear();
            
            // Hide with animation
            Hide();
            
            // Disable after animation
            DelayedDeactivate().Forget();
            
            OnTargetDeactivated?.Invoke();
        }
        
        private async UniTask DelayedDeactivate()
        {
            await UniTask.Delay(300); // Wait 300ms for hide animation
            if (!_isActive) // Check if still deactivated
            {
                gameObject.SetActive(false);
            }
        }
        
        private void UpdateTargetPosition()
        {
            if (!_isActive || _board == null) return;
            
            // Convert world position to pixel position
            var pixelPos = _board.GetPixelPositionInBoard(transform.position);
            _currentPixelPosition = new Vector2Int(Mathf.RoundToInt(pixelPos.x), Mathf.RoundToInt(pixelPos.y));
            
            // Calculate affected pixels in radius
            CalculateAffectedPixels();
            
            // Notify listeners
            OnTargetPositionChanged?.Invoke(_currentPixelPosition, _affectedPixels);
        }
        
        private void CalculateAffectedPixels()
        {
            _affectedPixels.Clear();
            
            if (Core.GameManager?.PixelWorld == null) return;
            
            var radiusInPixels = Mathf.RoundToInt(_bombRadius * Core.GameManager.PixelWorld.PixelsPerUnit);
            var centerX = _currentPixelPosition.x;
            var centerY = _currentPixelPosition.y;
            
            // Find all pixels within radius
            for (var y = centerY - radiusInPixels; y <= centerY + radiusInPixels; y++)
            {
                for (var x = centerX - radiusInPixels; x <= centerX + radiusInPixels; x++)
                {
                    // Check if pixel is within circular radius
                    var distance = Vector2.Distance(new Vector2(x, y), new Vector2(centerX, centerY));
                    if (distance <= radiusInPixels)
                    {
                        // Check if pixel exists and has material
                        if (Core.GameManager.PixelWorld.TryGetPixelAt(new Vector3(x, y, 0), out var pixel))
                        {
                            if (pixel.materialIndex > 0) // Not empty material
                            {
                                _affectedPixels.Add(new Vector2Int(x, y));
                            }
                        }
                    }
                }
            }
        }
        
        // Override draggable methods
        public override void HandleSelected(Vector3 worldPos, Rect boardRect)
        {
            if (!_isActive) return;
            base.HandleSelected(worldPos, boardRect);
        }
        
        public override void HandleMoving(Vector3 worldPos, Rect boardRect)
        {
            if (!_isActive) return;
            base.HandleMoving(worldPos, boardRect);
            
            // Update target position during movement
            UpdateTargetPosition();
        }
        
        public override void HandleRelease()
        {
            if (!_isActive) return;
            base.HandleRelease();
            
            // Final update when released
            UpdateTargetPosition();
        }
        
        protected override void OnSelected()
        {
            if (_targetIcon != null)
            {
                _targetIcon.color = Color.yellow; // Highlight when selected
            }
        }
        
        protected override void OnReleased()
        {
            if (_targetIcon != null)
            {
                _targetIcon.color = Color.white; // Normal color when released
            }
        }
        
        protected override void OnPlacementComplete()
        {
            // Not used for bomb target
        }
        
        public override void Reset()
        {
            base.Reset();
            DeactivateTarget();
        }
        
        // Override Show/Hide to handle target icon visibility
        public override void Show()
        {
            base.Show();
            if (_targetIcon != null)
            {
                _targetIcon.color = Color.white;
            }
        }
        
        public override void Hide()
        {
            base.Hide();
            if (_targetIcon != null)
            {
                _targetIcon.color = Color.clear;
            }
        }
    }
}
