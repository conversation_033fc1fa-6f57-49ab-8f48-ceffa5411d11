using System;
using System.Collections.Generic;
using Cysharp.Threading.Tasks;
using UnityEngine;
using PrimeTween;

namespace OP.BlockSand.Bomb
{
    /// <summary>
    /// Controller for bomb target icon that can be dragged around the board
    /// Custom drag-drop implementation specifically for bomb targeting
    /// </summary>
    public class BombTargetController : MonoBehaviour
    {
        [SerializeField]
        private float _bombRadius = 2f; // Radius in world units for bomb effect

        private Vector2Int _currentPixelPosition;
        private List<Vector2Int> _affectedPixels = new List<Vector2Int>();
        private Board _board;
        private bool _isActive = false;

        // Drag state
        private bool _isDragging = false;
        private Vector3 _dragOffset;
        private Camera _camera;
        private Rect _boardRect;

        public string Id { get; private set; } = "BombTarget";
        public Rect Rect => new Rect(-0.5f, -0.5f, 1f, 1f); // 1x1 unit rect for target icon

        public Vector2Int CurrentPixelPosition => _currentPixelPosition;
        public float BombRadius => _bombRadius;
        public bool IsActive => _isActive;

        private Transform _transform;
        public Transform OTransform => _transform ??= transform;

        // Events
        public event Action<Vector2Int, List<Vector2Int>> OnTargetPositionChanged;
        public event Action OnTargetActivated;
        public event Action OnTargetDeactivated;

        public void Initialize(Board board, Camera inputCamera)
        {
            _board = board;
            _camera = inputCamera;
            _isActive = false;

            // Hide initially
            gameObject.SetActive(false);
        }

        public void ActivateTarget(Vector3 initialWorldPosition, Rect boardRect)
        {
            if (_isActive) return;

            _isActive = true;
            _boardRect = boardRect;
            gameObject.SetActive(true);

            // Set initial position
            transform.position = initialWorldPosition;

            // Update pixel position and affected pixels
            UpdateTargetPosition();

            // Show with animation
            ShowTarget();

            OnTargetActivated?.Invoke();
        }

        public void DeactivateTarget()
        {
            if (!_isActive) return;

            _isActive = false;
            _isDragging = false;
            _affectedPixels.Clear();

            // Hide with animation
            HideTarget();

            // Disable after animation
            DelayedDeactivate().Forget();

            OnTargetDeactivated?.Invoke();
        }

        private async UniTask DelayedDeactivate()
        {
            await UniTask.Delay(300); // Wait 300ms for hide animation
            if (!_isActive) // Check if still deactivated
            {
                gameObject.SetActive(false);
            }
        }

        private void Update()
        {
            if (!_isActive) return;

            HandleInput();
        }

        private void HandleInput()
        {
            if (Input.GetMouseButtonDown(0))
            {
                HandleMouseDown();
            }
            else if (Input.GetMouseButton(0) && _isDragging)
            {
                HandleMouseDrag();
            }
            else if (Input.GetMouseButtonUp(0) && _isDragging)
            {
                HandleMouseUp();
            }
        }

        private void HandleMouseDown()
        {
            var mouseWorldPos = GetMouseWorldPosition();
            var targetBounds = new Bounds(OTransform.position, Vector3.one);

            // if (targetBounds.Contains(mouseWorldPos))
            {
                _isDragging = true;
                _dragOffset = OTransform.position - mouseWorldPos;

                // Scale up slightly
                Tween.Scale(OTransform, 1.1f, 0.1f);
            }

            DrawXXL.DrawBasics2D.PointTag(mouseWorldPos, $"HandleMouseDown: worldPos {mouseWorldPos}, targetBounds {targetBounds}");
        }

        private void HandleMouseDrag()
        {
            var mouseWorldPos = GetMouseWorldPosition();
            var targetPosition = mouseWorldPos + _dragOffset;

            // Clamp to board bounds
            targetPosition = ClampToBoardBounds(targetPosition);

            // Update position
            var delta = 120f * Time.deltaTime;
            OTransform.position = Vector3.MoveTowards(OTransform.position, targetPosition, delta);

            // Update affected pixels
            UpdateTargetPosition();

            DrawXXL.DrawBasics2D.PointTag(mouseWorldPos, $"HandleMouseDrag: worldPos {mouseWorldPos}, clamped {targetPosition}");
        }

        private void HandleMouseUp()
        {
            _isDragging = false;

            // Scale back to normal
            Tween.Scale(OTransform, 1f, 0.1f);

            // Final update
            UpdateTargetPosition();

            // Notify listeners
            OnTargetPositionChanged?.Invoke(_currentPixelPosition, _affectedPixels);
        }

        private Vector3 GetMouseWorldPosition()
        {
            if (_camera == null) return Vector3.zero;

            var mouseScreenPos = Input.mousePosition;
            return _camera.ScreenToWorldPoint(new Vector3(mouseScreenPos.x, mouseScreenPos.y, _camera.nearClipPlane));
        }

        private Vector3 ClampToBoardBounds(Vector3 position)
        {
            var halfSize = Rect.size * 0.5f;

            position.x = Mathf.Clamp(position.x, _boardRect.xMin + halfSize.x, _boardRect.xMax - halfSize.x);
            position.y = Mathf.Clamp(position.y, _boardRect.yMin + halfSize.y, _boardRect.yMax - halfSize.y);

            return position;
        }

        private void UpdateTargetPosition()
        {
            if (!_isActive || _board == null) return;

            // Convert world position to pixel position
            var pixelPos = _board.GetPixelPositionInBoard(transform.position);
            _currentPixelPosition = new Vector2Int(Mathf.RoundToInt(pixelPos.x), Mathf.RoundToInt(pixelPos.y));

            // Calculate affected pixels in radius
            CalculateAffectedPixels();
        }

        private void CalculateAffectedPixels()
        {
            _affectedPixels.Clear();

            if (Core.GameManager?.PixelWorld == null) return;

            var radiusInPixels = Mathf.RoundToInt(_bombRadius * Core.GameManager.PixelWorld.PixelsPerUnit);
            var centerX = _currentPixelPosition.x;
            var centerY = _currentPixelPosition.y;

            // Find all pixels within radius
            for (var y = centerY - radiusInPixels; y <= centerY + radiusInPixels; y++)
            {
                for (var x = centerX - radiusInPixels; x <= centerX + radiusInPixels; x++)
                {
                    // Check if pixel is within circular radius
                    var distance = Vector2.Distance(new Vector2(x, y), new Vector2(centerX, centerY));
                    if (distance <= radiusInPixels)
                    {
                        // Check if pixel exists and has material
                        if (Core.GameManager.PixelWorld.TryGetPixelAt(new Vector3(x, y, 0), out var pixel))
                        {
                            if (pixel.materialIndex > 0) // Not empty material
                            {
                                _affectedPixels.Add(new Vector2Int(x, y));
                            }
                        }
                    }
                }
            }
        }

        private void ShowTarget()
        {
            Tween.Scale(transform, 0f, 1f, 0.3f, Ease.OutBack);
        }

        private void HideTarget()
        {
            Tween.Scale(transform, 0f, 0.25f, Ease.InBack);
        }

        public void Reset()
        {
            _isDragging = false;
            DeactivateTarget();
        }
    }
}