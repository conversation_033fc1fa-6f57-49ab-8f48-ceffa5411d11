using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace OP.BlockSand.Bomb
{
    /// <summary>
    /// System to handle bomb preview animation - shows wave effect on affected pixels
    /// </summary>
    public class BombAnimationSystem : MonoBehaviour
    {
        [Header("Wave Animation Settings")]
        [SerializeField] private float _waveSpeed = 5f; // Speed of wave propagation
        [SerializeField] private float _waveDuration = 1f; // Duration of one complete wave cycle
        [SerializeField] private float _pixelAnimationDuration = 0.5f; // Duration for each pixel animation
        [SerializeField] private bool _loopAnimation = true; // Whether to loop the animation
        
        [Header("Color Animation")]
        [SerializeField] private Color32 _brighterColor = new Color32(255, 255, 255, 255); // Brighter version of original
        [SerializeField] private Color32 _whiteColor = new Color32(255, 255, 255, 255); // Pure white
        [SerializeField] private Color32 _whiteAlpha50 = new Color32(255, 255, 255, 128); // White with 50% alpha
        [SerializeField] private Color32 _whiteAlpha0 = new Color32(255, 255, 255, 0); // White with 0% alpha
        
        private PixelWorld _pixelWorld;
        private PixelMaterials _materialDefinition;
        private readonly List<BombPixelAnimationData> _animatingPixels = new();
        private Vector2Int _waveCenter;
        private Coroutine _animationCoroutine;
        
        public bool IsAnimating { get; private set; } = false;
        
        public void Initialize(PixelWorld pixelWorld, PixelMaterials materialDefinition)
        {
            _pixelWorld = pixelWorld;
            _materialDefinition = materialDefinition;
        }
        
        /// <summary>
        /// Start wave animation for bomb preview
        /// </summary>
        public void StartWaveAnimation(Vector2Int center, List<Vector2Int> affectedPixels)
        {
            if (IsAnimating)
            {
                StopAnimation();
            }
            
            _waveCenter = center;
            _animatingPixels.Clear();
            
            // Collect pixel data and calculate distances from center
            foreach (var pos in affectedPixels)
            {
                if (!_pixelWorld.TryGetPixelAt(pos.x, pos.y, out var pixel)) continue;
                
                var distance = Vector2.Distance(pos, center);
                var animData = new BombPixelAnimationData(
                    pos,
                    new Color32(pixel.r, pixel.g, pixel.b, pixel.a),
                    GetMaterialIdFromIndex(pixel.materialIndex),
                    distance
                );
                _animatingPixels.Add(animData);
            }
            
            if (_animatingPixels.Count > 0)
            {
                _animationCoroutine = StartCoroutine(AnimateWaveSequence());
            }
        }
        
        /// <summary>
        /// Stop current animation and restore original colors
        /// </summary>
        public void StopAnimation()
        {
            if (_animationCoroutine != null)
            {
                StopCoroutine(_animationCoroutine);
                _animationCoroutine = null;
            }
            
            // Restore original colors
            RestoreOriginalColors();
            
            IsAnimating = false;
            _animatingPixels.Clear();
        }
        
        private IEnumerator AnimateWaveSequence()
        {
            IsAnimating = true;
            
            do
            {
                yield return StartCoroutine(AnimateSingleWave());
                
                if (_loopAnimation)
                {
                    yield return new WaitForSeconds(0.2f); // Small pause between waves
                }
                
            } while (_loopAnimation && IsAnimating);
            
            IsAnimating = false;
        }
        
        private IEnumerator AnimateSingleWave()
        {
            var maxDistance = GetMaxDistance();
            var waveRadius = 0f;
            var elapsedTime = 0f;
            
            while (elapsedTime < _waveDuration && IsAnimating)
            {
                waveRadius = (elapsedTime / _waveDuration) * (maxDistance + 2f); // +2 to ensure all pixels are covered
                
                // Update each pixel based on wave position
                foreach (var animData in _animatingPixels)
                {
                    UpdatePixelAnimation(animData, waveRadius);
                }
                
                elapsedTime += Time.deltaTime;
                yield return null;
            }
            
            // Ensure all pixels complete their animation
            foreach (var animData in _animatingPixels)
            {
                CompletePixelAnimation(animData);
            }
        }
        
        private void UpdatePixelAnimation(BombPixelAnimationData animData, float waveRadius)
        {
            var distanceFromWave = Mathf.Abs(animData.distanceFromCenter - waveRadius);
            
            // Only animate pixels that are close to the wave front
            if (distanceFromWave <= 1f)
            {
                var animationProgress = 1f - distanceFromWave; // 1 = at wave front, 0 = far from wave
                var color = GetAnimationColor(animData.originalColor, animationProgress);
                
                _pixelWorld.DrawPixelForAnimation(animData.position.x, animData.position.y,
                    color.r, color.g, color.b, color.a, animData.materialId);
            }
            else if (waveRadius > animData.distanceFromCenter + 1f)
            {
                // Wave has passed, restore original color
                var originalColor = animData.originalColor;
                _pixelWorld.DrawPixelForAnimation(animData.position.x, animData.position.y,
                    originalColor.r, originalColor.g, originalColor.b, originalColor.a,
                    animData.materialId);
            }
        }
        
        private void CompletePixelAnimation(BombPixelAnimationData animData)
        {
            // Restore original color
            var originalColor = animData.originalColor;
            _pixelWorld.DrawPixelForAnimation(animData.position.x, animData.position.y,
                originalColor.r, originalColor.g, originalColor.b, originalColor.a,
                animData.materialId);
        }
        
        private Color32 GetAnimationColor(Color32 originalColor, float progress)
        {
            // Animation sequence: Original -> Brighter -> White -> White Alpha 50% -> White Alpha 0%
            if (progress < 0.25f)
            {
                // Original to Brighter
                var t = progress / 0.25f;
                return Color32.Lerp(originalColor, GetBrighterColor(originalColor), t);
            }
            else if (progress < 0.5f)
            {
                // Brighter to White
                var t = (progress - 0.25f) / 0.25f;
                return Color32.Lerp(GetBrighterColor(originalColor), _whiteColor, t);
            }
            else if (progress < 0.75f)
            {
                // White to White Alpha 50%
                var t = (progress - 0.5f) / 0.25f;
                return Color32.Lerp(_whiteColor, _whiteAlpha50, t);
            }
            else
            {
                // White Alpha 50% to White Alpha 0%
                var t = (progress - 0.75f) / 0.25f;
                return Color32.Lerp(_whiteAlpha50, _whiteAlpha0, t);
            }
        }
        
        private Color32 GetBrighterColor(Color32 originalColor)
        {
            // Make the original color brighter by increasing RGB values
            var brighterR = Mathf.Min(255, originalColor.r + 50);
            var brighterG = Mathf.Min(255, originalColor.g + 50);
            var brighterB = Mathf.Min(255, originalColor.b + 50);
            return new Color32((byte)brighterR, (byte)brighterG, (byte)brighterB, originalColor.a);
        }
        
        private float GetMaxDistance()
        {
            var maxDistance = 0f;
            foreach (var animData in _animatingPixels)
            {
                if (animData.distanceFromCenter > maxDistance)
                {
                    maxDistance = animData.distanceFromCenter;
                }
            }
            return maxDistance;
        }
        
        private void RestoreOriginalColors()
        {
            foreach (var animData in _animatingPixels)
            {
                var originalColor = animData.originalColor;
                _pixelWorld.DrawPixelForAnimation(animData.position.x, animData.position.y,
                    originalColor.r, originalColor.g, originalColor.b, originalColor.a,
                    animData.materialId);
            }
        }
        
        /// <summary>
        /// Convert material index to material ID using the pixel world's materials
        /// </summary>
        private PixelMaterialId GetMaterialIdFromIndex(int materialIndex)
        {
            if (materialIndex >= 0 && materialIndex < _materialDefinition.Materials.Length)
                return _materialDefinition.Materials[materialIndex].id;
            
            OLogger.LogError($"Invalid material index {materialIndex}!");
            return PixelMaterialId.Empty;
        }
    }
    
    /// <summary>
    /// Data structure for bomb pixel animation
    /// </summary>
    public struct BombPixelAnimationData
    {
        public Vector2Int position;
        public Color32 originalColor;
        public PixelMaterialId materialId;
        public float distanceFromCenter;
        
        public BombPixelAnimationData(Vector2Int pos, Color32 color, PixelMaterialId matId, float distance)
        {
            position = pos;
            originalColor = color;
            materialId = matId;
            distanceFromCenter = distance;
        }
    }
}
