using Cysharp.Threading.Tasks;
using Newtonsoft.Json;
using OnePuz.Audio;
using OnePuz.Data;
using OnePuz.Extensions;
using OnePuz.Manager;
using OnePuz.UI;
using OP.BlockSand.Shape;
using OP.BlockSand.Bomb;
using Sirenix.OdinInspector;
using UnityEngine;
using System.Collections.Generic;

namespace OP.BlockSand
{
    public struct OnGameViewChangedEvent
    {
    }

    public struct OnPlacedShapeEvent
    {
        public int pixelCount;
        public Vector3 worldPosition;
    }

    public struct OnClearSandClusterEvent
    {
        public int pixelCount;
        public PixelMaterialId materialId;
        public Vector3 worldPosition;
    }

    public class GameManager : BaseManager
    {
        [SerializeField]
        private Camera _camera;

        [SerializeField]
        private SandWorld _sandWorld;

        public PixelWorld PixelWorld => _sandWorld.PixelWorld;
        
        [SerializeField]
        private PixelMaterials _materialDefinition;

        [SerializeField]
        private ShapeSpawner _shapeSpawner;

        public ShapeSpawner ShapeSpawner => _shapeSpawner;

        [SerializeField]
        private PlayerInput _playerInput;

        [SerializeField]
        private Board _board;

        public Board Board => _board;

        [SerializeField]
        private Vector2 _gameViewSizeInUnit;

        [Header("Combo")]
        [SerializeField]
        private ComboController _comboController;

        [Header("Bomb System")]
        [SerializeField]
        private BombTargetController _bombTargetController;

        [SerializeField]
        private BombAnimationSystem _bombAnimationSystem;

        [SerializeField]
        private BombExplosionEffect _bombExplosionEffect;

        private bool _canPlaceBlock;
        private bool _placingBlock;

        // Bomb system state
        private bool _isBombModeActive = false;
        private List<Vector2Int> _currentBombAffectedPixels = new List<Vector2Int>();

        public override async UniTask LoadAsync()
        {
            _sandWorld.PixelWorld.FrameRate = 60;
            await UniTask.Yield();
            var loading = true;
            _sandWorld.LoadLevel(0, _ => loading = false);
            while (loading)
                await UniTask.Yield();

            _shapeSpawner.Init(new Vector2Int(_sandWorld.Width, _sandWorld.Height));
            _playerInput.Init(_camera);
            await _board.InitAsync(_sandWorld);

            // Initialize bomb system
            InitializeBombSystem();

            this.EventSubscribe<OnShapeSelectedEvent>(HandleSelectShape);
            this.EventSubscribe<OnShapeMovedEvent>(HandleMoveShape);
            this.EventSubscribe<OnShapeReleasedEvent>(HandleReleaseShape);
            this.EventSubscribe<OnShownPanelEvent>(HandleShownPanel);
            this.EventSubscribe<OnComboChangedEvent>(HandleOnComboChanged);
            this.EventSubscribe<OnScoreLevelUpEvent>(HandleOnScoreLevelUp);
        }

        public override UniTask UnloadAsync()
        {
            this.EventUnsubscribe<OnShapeSelectedEvent>(HandleSelectShape);
            this.EventUnsubscribe<OnShapeMovedEvent>(HandleMoveShape);
            this.EventUnsubscribe<OnShapeReleasedEvent>(HandleReleaseShape);
            this.EventUnsubscribe<OnShownPanelEvent>(HandleShownPanel);
            this.EventUnsubscribe<OnComboChangedEvent>(HandleOnComboChanged);
            this.EventUnsubscribe<OnScoreLevelUpEvent>(HandleOnScoreLevelUp);
            return UniTask.CompletedTask;
        }

        public override async UniTask ResetAsync()
        {
            _shapeSpawner.Reset();
            _shapeSpawner.SpawnShapesAsync(true).Forget();

            _board.ResetGameState();
            Core.Score.Reset();

            var loading = true;
            _sandWorld.LoadLevel(0, _ => loading = false);
            while (loading)
                await UniTask.Yield();

            _canPlaceBlock = true;
            _placingBlock = false;
        }

        public override void Activate()
        {
            _canPlaceBlock = true;

            AudioShortcut.PlayGameStart();
        }

        private void Update()
        {
            if (!IsInitialized)
                return;

            _board.UpdateGameStateCheck();
            if (_board.ShouldCheckGameState())
            {
                _board.CheckGameState();
            }
        }

        private void HandleSelectShape(OnShapeSelectedEvent e)
        {
            var shape = _shapeSpawner.GetShape(e.shapeIndex);
            if (!shape) return;
            // AudioShortcut.PlayChooseBlock();
            // Core.Vibration.VibrateLight();
            shape.HandleSelected(e.worldPosition, _board.DrawableRect);
        }

        private void HandleMoveShape(OnShapeMovedEvent e)
        {
            var shape = _shapeSpawner.GetShape(e.shapeIndex);
            if (!shape) return;

            shape.HandleMoving(e.worldPosition, _board.DrawableRect);
            _canPlaceBlock = _board.ValidateDrawShape(shape, out _, out _);

            if (_placingBlock)
                return;
        }

        private void HandleReleaseShape(OnShapeReleasedEvent e)
        {
            var shape = _shapeSpawner.GetShape(e.shapeIndex);
            if (!shape) return;

            shape.HandleRelease();

            if (_canPlaceBlock && !_placingBlock && !e.hasCanceled)
            {
                PlaceShapeAsync(shape, e.shapeIndex).Forget();
            }
            else
            {
                AudioShortcut.PlayPlacedFailed();
                shape.MoveBack();
            }   
        }

        private async UniTask PlaceShapeAsync(ShapeController shape, int shapeIndex)
        {
            _placingBlock = true;
            _canPlaceBlock = false;

            var valid = _board.ValidateDrawShape(shape, out var targetShapePosition, out var pixelPosition);
            if (valid)
            {
                var shapeMoving = true;
                shape.MoveToTarget(targetShapePosition, () => shapeMoving = false);
                await UniTask.WaitUntil(() => !shapeMoving);

                Core.Vibration.VibrateLight();
                _board.DrawShape(shape, pixelPosition);
                _shapeSpawner.RemoveShape(shapeIndex);
                _shapeSpawner.Despawn(shape);
                AudioShortcut.PlayDropSand();

                Core.Event.Fire(new OnPlacedShapeEvent() { pixelCount = shape.GetPixelCount(), worldPosition = targetShapePosition });

                if (!_shapeSpawner.AnyShapesLeft())
                    _shapeSpawner.SpawnShapesAsync(showImmediately: true).Forget();
            }
            else
            {
                AudioShortcut.PlayPlacedFailed();
                shape.MoveBack();
            }

            _placingBlock = false;
            _canPlaceBlock = true;
        }

        private void HandleShownPanel(OnShownPanelEvent e)
        {
            if (e.panelId != UIKeys.Panel.GAME) return;
            var gamePanel = Core.UI.Get<UIPanelGame>();
            if (!gamePanel) return;
            var gameViewRectTransform = gamePanel.GetGameBounds();
            var gameBound = gameViewRectTransform.rect;
            var canvasBound = gamePanel.pCanvas.GetComponent<RectTransform>().rect;
            var gameViewRatio = gameBound.height / canvasBound.height;
            var orthoSize = SetupMultiResolution(gameViewRatio);

            var boardPositionY = gameViewRectTransform.anchoredPosition.y / canvasBound.height * 2f * orthoSize + 1.0f;
            _sandWorld.transform.position = new Vector3(0, boardPositionY, 0);

            Core.Event.Fire(new OnGameViewChangedEvent());
        }

        private float SetupMultiResolution(float ratio = 0.68f)
        {
            var expectedOrthoSize = _gameViewSizeInUnit.y / ratio / 2f;
            var width = expectedOrthoSize * 2f * _camera.aspect;
            if (width < _gameViewSizeInUnit.x)
            {
                expectedOrthoSize = _gameViewSizeInUnit.x / 2f / _camera.aspect;
            }

            _camera.orthographicSize = expectedOrthoSize;
            return expectedOrthoSize;
        }

        private void HandleOnComboChanged(OnComboChangedEvent e)
        {
            if (e.currentCombo <= 1 || e.isComboBreak) return;
            _comboController.Show(e.currentCombo);
        }

        private void HandleOnScoreLevelUp(OnScoreLevelUpEvent e)
        {
            _shapeSpawner.MoveBackAllShapes();
            UIShortcut.ShowPopup(UIKeys.Panel.POPUP_LEVEL_UP);
            if (_shapeSpawner.JustUnlockedNewColor)
            {
                UIShortcut.EnqueuePopup(UIKeys.Panel.POPUP_UNLOCK_COLOR, PanelArgs.Popup.AddData("unlockedBlockType", Core.GameManager.ShapeSpawner.NewUnlockedColor));
            }
        }

        [Button]
        public void SaveBoardToJson()
        {
            DataShortcut.Board.ManualSave();
            var json = JsonConvert.SerializeObject(DataShortcut.Board.activePixels);
            System.IO.File.WriteAllText(Application.dataPath + "/tutorial.json", json);
            OLogger.LogNotice($"Saved board to {Application.dataPath}/tutorial.json");
        }

        [Button]
        public void SpawnAllShapesWithType(BlockType type)
        {
            _shapeSpawner.SpawnAllShapesWithType(type);
        }

        #region Bomb System

        /// <summary>
        /// Initialize bomb system components
        /// </summary>
        private void InitializeBombSystem()
        {
            if (_bombTargetController != null)
            {
                _bombTargetController.Initialize(_board);
                _bombTargetController.OnTargetPositionChanged += HandleBombTargetPositionChanged;
                _bombTargetController.OnTargetActivated += HandleBombTargetActivated;
                _bombTargetController.OnTargetDeactivated += HandleBombTargetDeactivated;
            }

            if (_bombAnimationSystem != null)
            {
                _bombAnimationSystem.Initialize(PixelWorld, _materialDefinition);
            }

            if (_bombExplosionEffect != null)
            {
                _bombExplosionEffect.Initialize(PixelWorld, _materialDefinition);
            }
        }

        /// <summary>
        /// Check if bomb mode is currently active
        /// </summary>
        public bool IsBombModeActive => _isBombModeActive;

        /// <summary>
        /// Activate bomb mode - called from UI button
        /// </summary>
        public void ActivateBombMode()
        {
            if (_isBombModeActive || _bombTargetController == null)
                return;

            // Prevent normal shape placement
            _canPlaceBlock = false;
            _isBombModeActive = true;

            // Activate board bomb mode
            _board.ActivateBombMode();

            // Show bomb target at center of board
            var centerPosition = _board.transform.position;
            _bombTargetController.ActivateTarget(centerPosition);

            OLogger.Log("Bomb mode activated");
        }

        /// <summary>
        /// Deactivate bomb mode
        /// </summary>
        public void DeactivateBombMode()
        {
            if (!_isBombModeActive)
                return;

            _isBombModeActive = false;

            // Stop any ongoing animations and effects
            if (_bombAnimationSystem != null)
            {
                _bombAnimationSystem.StopAnimation();
            }

            if (_bombExplosionEffect != null)
            {
                _bombExplosionEffect.StopExplosion();
            }

            // Deactivate bomb target
            if (_bombTargetController != null)
            {
                _bombTargetController.DeactivateTarget();
            }

            // Deactivate board bomb mode
            _board.DeactivateBombMode();

            // Re-enable normal shape placement
            _canPlaceBlock = true;

            _currentBombAffectedPixels.Clear();

            OLogger.Log("Bomb mode deactivated");
        }

        /// <summary>
        /// Execute bomb explosion at current target position
        /// </summary>
        public void ExecuteBombExplosion()
        {
            if (!_isBombModeActive || _bombTargetController == null || !_bombTargetController.IsActive)
            {
                OLogger.LogWarning("Cannot execute bomb - bomb mode not active or no target");
                return;
            }

            var targetPosition = _bombTargetController.CurrentPixelPosition;
            var bombRadius = _bombTargetController.BombRadius;

            // Start flood fill calculation
            _board.StartBombFloodFill(targetPosition, bombRadius, OnBombFloodFillComplete);
        }

        private void OnBombFloodFillComplete(List<Vector2Int> affectedPixels)
        {
            if (affectedPixels == null || affectedPixels.Count == 0)
            {
                OLogger.Log("No pixels affected by bomb");
                DeactivateBombMode();
                return;
            }

            _currentBombAffectedPixels = affectedPixels;

            // Stop preview animation
            if (_bombAnimationSystem != null)
            {
                _bombAnimationSystem.StopAnimation();
            }

            // Start explosion effect
            if (_bombExplosionEffect != null && _bombTargetController != null)
            {
                var explosionPosition = _bombTargetController.transform.position;
                _bombExplosionEffect.StartExplosionEffect(explosionPosition, affectedPixels, OnBombExplosionComplete).Forget();
            }
            else
            {
                // Fallback to direct clearing
                _board.ClearBombPixels(affectedPixels, OnBombExplosionComplete);
            }

            // Play explosion sound and effects
            AudioShortcut.PlayDropSand(); // Use existing sound for now
            Core.Vibration.VibrateHeavy();

            OLogger.Log($"Bomb exploded! Cleared {affectedPixels.Count} pixels");
        }

        private void OnBombExplosionComplete()
        {
            // Deactivate bomb mode after explosion
            DeactivateBombMode();

            // Fire event for score/combo system
            if (_currentBombAffectedPixels.Count > 0)
            {
                var centerPosition = _bombTargetController != null ?
                    _bombTargetController.transform.position : Vector3.zero;

                Core.Event.Fire(new OnClearSandClusterEvent()
                {
                    pixelCount = _currentBombAffectedPixels.Count,
                    materialId = PixelMaterialId.Sand, // Default to sand for now
                    worldPosition = centerPosition
                });
            }
        }

        private void HandleBombTargetPositionChanged(Vector2Int position, List<Vector2Int> affectedPixels)
        {
            // Update preview animation
            if (_bombAnimationSystem != null && affectedPixels.Count > 0)
            {
                _bombAnimationSystem.StartWaveAnimation(position, affectedPixels);
            }
        }

        private void HandleBombTargetActivated()
        {
            // Target activated - could add UI feedback here
        }

        private void HandleBombTargetDeactivated()
        {
            // Target deactivated - stop any preview animations
            if (_bombAnimationSystem != null)
            {
                _bombAnimationSystem.StopAnimation();
            }
        }

        #endregion
    }
}