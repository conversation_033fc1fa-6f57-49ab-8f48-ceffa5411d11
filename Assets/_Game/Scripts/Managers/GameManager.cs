using Cysharp.Threading.Tasks;
using Newtonsoft.Json;
using OnePuz.Audio;
using OnePuz.Data;
using OnePuz.Extensions;
using OnePuz.Manager;
using OnePuz.UI;
using OP.BlockSand.Shape;
using Sirenix.OdinInspector;
using UnityEngine;

namespace OP.BlockSand
{
    public struct OnGameViewChangedEvent
    {
    }

    public struct OnPlacedShapeEvent
    {
        public int pixelCount;
        public Vector3 worldPosition;
    }

    public struct OnClearSandClusterEvent
    {
        public int pixelCount;
        public PixelMaterialId materialId;
        public Vector3 worldPosition;
    }

    public class GameManager : BaseManager
    {
        [SerializeField]
        private Camera _camera;

        [SerializeField]
        private SandWorld _sandWorld;

        public PixelWorld PixelWorld => _sandWorld.PixelWorld;

        [SerializeField]
        private ShapeSpawner _shapeSpawner;

        public ShapeSpawner ShapeSpawner => _shapeSpawner;

        [SerializeField]
        private PlayerInput _playerInput;

        [SerializeField]
        private Board _board;

        public Board Board => _board;

        [SerializeField]
        private Vector2 _gameViewSizeInUnit;

        [Header("Combo")]
        [SerializeField]
        private ComboController _comboController;

        private bool _canPlaceBlock;
        private bool _placingBlock;

        public override async UniTask LoadAsync()
        {
            _sandWorld.PixelWorld.FrameRate = 60;
            await UniTask.Yield();
            var loading = true;
            _sandWorld.LoadLevel(0, _ => loading = false);
            while (loading)
                await UniTask.Yield();

            _shapeSpawner.Init(new Vector2Int(_sandWorld.Width, _sandWorld.Height));
            _playerInput.Init(_camera);
            await _board.InitAsync(_sandWorld);

            this.EventSubscribe<OnShapeSelectedEvent>(HandleSelectShape);
            this.EventSubscribe<OnShapeMovedEvent>(HandleMoveShape);
            this.EventSubscribe<OnShapeReleasedEvent>(HandleReleaseShape);
            this.EventSubscribe<OnShownPanelEvent>(HandleShownPanel);
            this.EventSubscribe<OnComboChangedEvent>(HandleOnComboChanged);
            this.EventSubscribe<OnScoreLevelUpEvent>(HandleOnScoreLevelUp);
        }

        public override UniTask UnloadAsync()
        {
            this.EventUnsubscribe<OnShapeSelectedEvent>(HandleSelectShape);
            this.EventUnsubscribe<OnShapeMovedEvent>(HandleMoveShape);
            this.EventUnsubscribe<OnShapeReleasedEvent>(HandleReleaseShape);
            this.EventUnsubscribe<OnShownPanelEvent>(HandleShownPanel);
            this.EventUnsubscribe<OnComboChangedEvent>(HandleOnComboChanged);
            this.EventUnsubscribe<OnScoreLevelUpEvent>(HandleOnScoreLevelUp);
            return UniTask.CompletedTask;
        }

        public override async UniTask ResetAsync()
        {
            _shapeSpawner.Reset();
            _shapeSpawner.SpawnShapesAsync(true).Forget();

            _board.ResetGameState();
            Core.Score.Reset();

            var loading = true;
            _sandWorld.LoadLevel(0, _ => loading = false);
            while (loading)
                await UniTask.Yield();

            _canPlaceBlock = true;
            _placingBlock = false;
        }

        public override void Activate()
        {
            _canPlaceBlock = true;

            AudioShortcut.PlayGameStart();
        }

        private void Update()
        {
            if (!IsInitialized)
                return;

            _board.UpdateGameStateCheck();
            if (_board.ShouldCheckGameState())
            {
                _board.CheckGameState();
            }
        }

        private void HandleSelectShape(OnShapeSelectedEvent e)
        {
            var shape = _shapeSpawner.GetShape(e.shapeIndex);
            if (!shape) return;
            // AudioShortcut.PlayChooseBlock();
            // Core.Vibration.VibrateLight();
            shape.HandleSelected(e.worldPosition, _board.DrawableRect);
        }

        private void HandleMoveShape(OnShapeMovedEvent e)
        {
            var shape = _shapeSpawner.GetShape(e.shapeIndex);
            if (!shape) return;

            shape.HandleMoving(e.worldPosition, _board.DrawableRect);
            _canPlaceBlock = _board.ValidateDrawShape(shape, out _, out _);

            if (_placingBlock)
                return;
        }

        private void HandleReleaseShape(OnShapeReleasedEvent e)
        {
            var shape = _shapeSpawner.GetShape(e.shapeIndex);
            if (!shape) return;

            shape.HandleRelease();

            if (_canPlaceBlock && !_placingBlock && !e.hasCanceled)
            {
                PlaceShapeAsync(shape, e.shapeIndex).Forget();
            }
            else
            {
                AudioShortcut.PlayPlacedFailed();
                shape.MoveBack();
            }   
        }

        private async UniTask PlaceShapeAsync(ShapeController shape, int shapeIndex)
        {
            _placingBlock = true;
            _canPlaceBlock = false;

            var valid = _board.ValidateDrawShape(shape, out var targetShapePosition, out var pixelPosition);
            if (valid)
            {
                var shapeMoving = true;
                shape.MoveToTarget(targetShapePosition, () => shapeMoving = false);
                await UniTask.WaitUntil(() => !shapeMoving);

                Core.Vibration.VibrateLight();
                _board.DrawShape(shape, pixelPosition);
                _shapeSpawner.RemoveShape(shapeIndex);
                _shapeSpawner.Despawn(shape);
                AudioShortcut.PlayDropSand();

                Core.Event.Fire(new OnPlacedShapeEvent() { pixelCount = shape.GetPixelCount(), worldPosition = targetShapePosition });

                if (!_shapeSpawner.AnyShapesLeft())
                    _shapeSpawner.SpawnShapesAsync(showImmediately: true).Forget();
            }
            else
            {
                AudioShortcut.PlayPlacedFailed();
                shape.MoveBack();
            }

            _placingBlock = false;
            _canPlaceBlock = true;
        }

        private void HandleShownPanel(OnShownPanelEvent e)
        {
            if (e.panelId != UIKeys.Panel.GAME) return;
            var gamePanel = Core.UI.Get<UIPanelGame>();
            if (!gamePanel) return;
            var gameViewRectTransform = gamePanel.GetGameBounds();
            var gameBound = gameViewRectTransform.rect;
            var canvasBound = gamePanel.pCanvas.GetComponent<RectTransform>().rect;
            var gameViewRatio = gameBound.height / canvasBound.height;
            var orthoSize = SetupMultiResolution(gameViewRatio);

            var boardPositionY = gameViewRectTransform.anchoredPosition.y / canvasBound.height * 2f * orthoSize + 1.0f;
            _sandWorld.transform.position = new Vector3(0, boardPositionY, 0);

            Core.Event.Fire(new OnGameViewChangedEvent());
        }

        private float SetupMultiResolution(float ratio = 0.68f)
        {
            var expectedOrthoSize = _gameViewSizeInUnit.y / ratio / 2f;
            var width = expectedOrthoSize * 2f * _camera.aspect;
            if (width < _gameViewSizeInUnit.x)
            {
                expectedOrthoSize = _gameViewSizeInUnit.x / 2f / _camera.aspect;
            }

            _camera.orthographicSize = expectedOrthoSize;
            return expectedOrthoSize;
        }

        private void HandleOnComboChanged(OnComboChangedEvent e)
        {
            if (e.currentCombo <= 1 || e.isComboBreak) return;
            _comboController.Show(e.currentCombo);
        }

        private void HandleOnScoreLevelUp(OnScoreLevelUpEvent e)
        {
            _shapeSpawner.MoveBackAllShapes();
            UIShortcut.ShowPopup(UIKeys.Panel.POPUP_LEVEL_UP);
            if (_shapeSpawner.JustUnlockedNewColor)
            {
                UIShortcut.EnqueuePopup(UIKeys.Panel.POPUP_UNLOCK_COLOR, PanelArgs.Popup.AddData("unlockedBlockType", Core.GameManager.ShapeSpawner.NewUnlockedColor));
            }
        }

        [Button]
        public void SaveBoardToJson()
        {
            DataShortcut.Board.ManualSave();
            var json = JsonConvert.SerializeObject(DataShortcut.Board.activePixels);
            System.IO.File.WriteAllText(Application.dataPath + "/tutorial.json", json);
            OLogger.LogNotice($"Saved board to {Application.dataPath}/tutorial.json");
        }

        [Button]
        public void SpawnAllShapesWithType(BlockType type)
        {
            _shapeSpawner.SpawnAllShapesWithType(type);
        }
    }
}