using System.Collections.Generic;
using System.Reflection;
using OnePuz.Utilities;
using UnityEngine;

namespace OnePuz.Services
{
    public class EffectService : IService
    {
        private readonly Dictionary<string, GameObject> _fxPrefabs = new();

        public void Warmup(GameState state)
        {
            if (state != GameState.GAME) return;

            var fieldKeys = typeof(FxKeys.Game).GetFields(BindingFlags.Static | BindingFlags.Public);
            foreach (var info in fieldKeys)
            {
                var rawValue = info.GetRawConstantValue().ToString();
                _fxPrefabs.TryAdd(rawValue, Resources.Load<GameObject>(rawValue));
                Core.ScenePool.WarmupCapacity(_fxPrefabs[rawValue], 1);
            }
        }

        public GameObject Spawn(string key, Vector3 position, Transform parent = null, bool autoRecycle = true, bool autoPlay = true,
            SimpleCallback callback = null)
        {
            var fx = Core.ScenePool.Spawn(_fxPrefabs[key], parent, position, _fxPrefabs[key].transform.rotation);
            if (autoPlay)
                fx.GetComponent<ParticleSystem>().Play();

            var particleSystemAutoRecycle = fx.GetComponent<ParticleSystemAutoRecycle>();
            if (!particleSystemAutoRecycle) particleSystemAutoRecycle = fx.AddComponent<ParticleSystemAutoRecycle>();
            particleSystemAutoRecycle.Init(autoRecycle, false, callback);

            return fx;
        }
        
        public GameObject Spawn(string key, Vector3 position, Vector3 scale, Transform parent = null, bool autoRecycle = true, bool autoPlay = true,
            SimpleCallback callback = null)
        {
            var fx = Core.ScenePool.Spawn(_fxPrefabs[key], parent, position, _fxPrefabs[key].transform.rotation);
            fx.transform.localScale = scale;
            if (autoPlay)
                fx.GetComponent<ParticleSystem>().Play();

            var particleSystemAutoRecycle = fx.GetComponent<ParticleSystemAutoRecycle>();
            if (!particleSystemAutoRecycle) particleSystemAutoRecycle = fx.AddComponent<ParticleSystemAutoRecycle>();
            particleSystemAutoRecycle.Init(autoRecycle, false, callback);

            return fx;
        }
    }
}