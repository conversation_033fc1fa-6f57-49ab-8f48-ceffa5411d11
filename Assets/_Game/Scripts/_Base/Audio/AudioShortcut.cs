using System.Collections.Generic;
using UnityEngine;

namespace OnePuz.Audio
{
    public static class AudioShortcut
    {
        private static AudioService _service;
        public static AudioService Instance => _service ??= Core.Audio;

        public static void PlayClicked() =>
            Instance.PlaySound(AudioKey.Click, 1f);

        public static void PlayCollectItem() =>
            Instance.PlaySound(AudioKey.CollectItem, volume: 0.3f);

        public static void PlayPopupIn() =>
            Instance.PlaySound(AudioKey.PopupIn, 0.3f);

        public static void PlayPopupOut() =>
            Instance.PlaySound(AudioKey.PopupOut, 0.3f);

        public static void PlayVictory() =>
            Instance.PlaySound(AudioKey.Victory, .25f);

        public static void PlayTimeOut() =>
            Instance.PlaySound(AudioKey.Timeout, 1);

        public static void PlayCuteSfx() =>
            Instance.PlaySound(AudioKey.CuteSfx, 0.75f);

        public static void PlayWarning() =>
            Instance.PlaySound(AudioKey.Warning, .45f);

        public static void PlayConfetti() => Instance.PlaySound(AudioKey.Confetti, .2f);

        public static void PlayParticleCollision() => Instance.PlaySound(AudioKey.BrokenBlockCollisionClip);
        public static void PlayDisplayingBlock() => Instance.PlaySound(AudioKey.DisplayingBlockClip);
        public static void PlayFireWork() => Instance.PlaySound(AudioKey.FireWorkClip);
        public static void PlaySpin() => Instance.PlaySound(AudioKey.SpinClip);
        public static void PlayTickTimer() => Instance.PlaySound(AudioKey.TickTimerClip);
        public static void PlayVoiceGood() => Instance.PlaySound(AudioKey.GoodClip, volume: 0.5f);
        public static void PlayVoiceGreat() => Instance.PlaySound(AudioKey.GreatClip, volume: 0.5f);
        public static void PlayVoiceAmazing() => Instance.PlaySound(AudioKey.AmazingClip, volume: 0.5f);
        public static void PlayHighScore() => Instance.PlaySound(AudioKey.HighScoreClip);
        public static void PlayGotSpin() => Instance.PlaySound(AudioKey.GotSpinClip);
        public static void PlayLose() => Instance.PlaySound(AudioKey.LoseClip);
        public static void PlayChooseBlock() => Instance.PlaySound(AudioKey.ChooseBlockClip);
        public static void PlayPlacedFailed() => Instance.PlaySound(AudioKey.PlacedFailedClip);
        public static void PlayDropSand() => Instance.PlaySound(AudioKey.DropSandClip);

        private static readonly List<string> _matchingSandSounds = new List<string>
        {
            // AudioKey.SandMatch_1, AudioKey.SandMatch_2, AudioKey.SandMatch_3, AudioKey.SandMatch_4, AudioKey.SandMatch_5, AudioKey.SandMatch_6, AudioKey.SandMatch_7, AudioKey.SandMatch_8, AudioKey.SandMatch_9, AudioKey.SandMatch_10
            // AudioKey.Clear_0, AudioKey.Clear_1, AudioKey.Clear_2, AudioKey.Clear_3, AudioKey.Clear_4, AudioKey.Clear_5, AudioKey.Clear_6, AudioKey.Clear_7, AudioKey.Clear_8, AudioKey.Clear_9, AudioKey.Clear_10, AudioKey.Clear_11, AudioKey.Clear_12, AudioKey.Clear_13, AudioKey.Clear_14, AudioKey.Clear_15
            AudioKey.Combo_0, AudioKey.Combo_1, AudioKey.Combo_2, AudioKey.Combo_3, AudioKey.Combo_4
        };

        public static void PlaySandMatch(int currentCombo)
        {
            Instance.PlaySound(_matchingSandSounds[currentCombo % _matchingSandSounds.Count], volume: 0.35f);
        }
        
        public static void PlayGameStart() => Instance.PlaySound(AudioKey.GameStartClip, volume: 0.35f);

        private static void PlayMusicHome() =>
            Instance.PlayMusic(AudioKey.MusicHome);

        private static void PlayMusicGameplay() =>
            Instance.PlayMusic(AudioKey.MusicGameplay, 0.35f);

        public static void PlayMusic(GameState state)
        {
            switch (state)
            {
                case GameState.HOME:
                    // PlayMusicHome();
                    break;
                case GameState.GAME:
                    // PlayMusicGameplay();
                    break;
            }
        }

        public static void StopMusic() => Instance.StopMusic();
    }
}