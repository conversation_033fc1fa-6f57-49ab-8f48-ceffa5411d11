namespace OnePuz.Audio
{
    public partial class AudioKey
    {
        public const string MusicHome = "Musics/home";
        public const string MusicGameplay = "Musics/in-game";
        
        public const string Typing = "Sounds/typing";
        public const string Victory = "Sounds/win_screen";
        public const string Lose = "Sounds/level_lose";
        public const string Ticktock = "Sounds/Ticktock";
        public const string Timeout = "Sounds/TimeOut";
        public const string Warning = "Sounds/Warning";
        public const string Whoosh = "Sounds/Whoosh";
        public const string CollectItem = "Sounds/Collect item";
        public const string CuteSfx = "Sounds/CuteSfx";
        public const string Put = "Sounds/Click_Old";
        public const string OpenChest = "Sounds/OpenChest";
        
        public const string PopupIn = "Sounds/PopIn";
        public const string PopupOut = "Sounds/PopOut";
        
        public const string Confetti = "Sounds/Confetti";
        
        public const string ChooseBlockClip = "Sounds/BlockPuzzle/undo";
        public const string DisplayingBlockClip = "Sounds/BlockPuzzle/collect_item_sparkle_pop_10";
        public const string DropSandClip = "Sounds/Sand/DropSand";
        public const string PlacedFailedClip = "Sounds/BlockPuzzle/blockwrong";
        public const string SandMatch_1 = "Sounds/Sand/resolve_1";
        public const string SandMatch_2 = "Sounds/Sand/resolve_2";
        public const string SandMatch_3 = "Sounds/Sand/resolve_3";
        public const string SandMatch_4 = "Sounds/Sand/resolve_4";
        public const string SandMatch_5 = "Sounds/Sand/resolve_5";
        public const string SandMatch_6 = "Sounds/Sand/resolve_6";
        public const string SandMatch_7 = "Sounds/Sand/resolve_7";
        public const string SandMatch_8 = "Sounds/Sand/resolve_8";
        public const string SandMatch_9 = "Sounds/Sand/resolve_9";
        public const string SandMatch_10 = "Sounds/Sand/resolve_10";
        public const string HighScoreClip = "Sounds/BlockPuzzle/newRecord";
        public const string LoseClip = "Sounds/BlockPuzzle/over";
        public const string BrokenBlockCollisionClip = "Sounds/BlockPuzzle/particle_collision";
        public const string SpinClip = "Sounds/BlockPuzzle/Spin";
        public const string FireWorkClip = "Sounds/BlockPuzzle/fireworks";
        public const string TickTimerClip = "Sounds/BlockPuzzle/ticking_timer";
        public const string GotSpinClip = "Sounds/BlockPuzzle/spin_get";
        public const string GoodClip = "Sounds/BlockPuzzle/effectGood";
        public const string GreatClip = "Sounds/BlockPuzzle/effectGreat";
        public const string PerfectClip = "Sounds/BlockPuzzle/effectPerfect";
        public const string AmazingClip = "Sounds/BlockPuzzle/effectAmazing";
        public const string GameStartClip = "Sounds/Sand/game_start";
        
        public const string Clear_0 = "Sounds/Sand/Clear/clear_0";
        public const string Clear_1 = "Sounds/Sand/Clear/clear_1";
        public const string Clear_2 = "Sounds/Sand/Clear/clear_2";
        public const string Clear_3 = "Sounds/Sand/Clear/clear_3";
        public const string Clear_4 = "Sounds/Sand/Clear/clear_4";
        public const string Clear_5 = "Sounds/Sand/Clear/clear_5";
        public const string Clear_6 = "Sounds/Sand/Clear/clear_6";
        public const string Clear_7 = "Sounds/Sand/Clear/clear_7";
        public const string Clear_8 = "Sounds/Sand/Clear/clear_8";
        public const string Clear_9 = "Sounds/Sand/Clear/clear_9";
        public const string Clear_10 = "Sounds/Sand/Clear/clear_10";
        public const string Clear_11 = "Sounds/Sand/Clear/clear_11";
        public const string Clear_12 = "Sounds/Sand/Clear/clear_12";
        public const string Clear_13 = "Sounds/Sand/Clear/clear_13";
        public const string Clear_14 = "Sounds/Sand/Clear/clear_14";
        public const string Clear_15 = "Sounds/Sand/Clear/clear_15";
        
        public const string Combo_0 = "Sounds/Sand/Combo/Combo_1";
        public const string Combo_1 = "Sounds/Sand/Combo/Combo_2";
        public const string Combo_2 = "Sounds/Sand/Combo/Combo_3";
        public const string Combo_3 = "Sounds/Sand/Combo/Combo_4";
        public const string Combo_4 = "Sounds/Sand/Combo/Combo_5";
    }
}