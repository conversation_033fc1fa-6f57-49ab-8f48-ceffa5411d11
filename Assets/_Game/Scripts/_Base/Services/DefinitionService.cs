using OnePuz.DailyReward;
using OnePuz.DailyReward.Definitions;
using OnePuz.Data;
using OnePuz.Definition;
using OnePuz.Level;
using OnePuz.Live;
using OnePuz.Shop.Definition;
using OnePuz.UI;
using OP.BlockSand;
using OP.BlockSand.Shape;
using UnityEngine;
using UnityEngine.Serialization;

namespace OnePuz.Services
{
    public class DefinitionService : MonoBehaviour, IServiceLoad
    {
        [SerializeField] private UIPrefabDefinitions _uiPrefabDefinition;
        public UIPrefabDefinitions UIPrefabs => _uiPrefabDefinition;

        [SerializeField] private GameDefinition _gameDefinition;
        public GameDefinition Game => _gameDefinition;

        [SerializeField] private LevelGroupDefinition _levelGroupDefinition;
        public LevelGroupDefinition LevelGroup => _levelGroupDefinition;

        [SerializeField] private ShopDefinition _shopDefinition;
        public ShopDefinition Shop => _shopDefinition;

        [SerializeField] private DailyRewardDefinition _dailyRewardDefinition;
        public DailyRewardDefinition DailyReward => _dailyRewardDefinition;

        [SerializeField] private CurrencyDefinition _currencyDefinition;
        public CurrencyDefinition Currency => _currencyDefinition;
        
        [SerializeField] private LiveDefinition _liveDefinition;
        public LiveDefinition Live => _liveDefinition;

        [SerializeField] private BoosterDefinition _boosterDefinition;
        public BoosterDefinition Booster => _boosterDefinition;

        [FormerlySerializedAs("_rewardDefinition")] [SerializeField] private RewardDefinition _rewardsDefinition;
        public RewardDefinition Rewards => _rewardsDefinition;

        [SerializeField] private LevelChestDefinition _levelChestDefinition;
        public LevelChestDefinition LevelChest => _levelChestDefinition;

        [SerializeField] private TutorialDefinition _tutorialDefinition;
        public TutorialDefinition Tutorial => _tutorialDefinition;
        
        [SerializeField] private ScoreDefinition _scoreDefinition;
        public ScoreDefinition Score => _scoreDefinition;
        
        [SerializeField] private SpawningRuleDefinition _shapeSpawningRuleDefinition;
        public SpawningRuleDefinition ShapeSpawningRule => _shapeSpawningRuleDefinition;

        public void Load()
        {
            OLogger.Log($"Load");
        }

        public BaseDefinitionData GetRewardDefinition(RewardData rewardData)
        {
            return rewardData.RewardType switch
            {
                RewardType.CURRENCY => Currency.GetDefinition(rewardData.CurrencyType),
                RewardType.BOOSTER => Booster.GetDefinition(rewardData.BoosterType),
#if USE_SKIN_SHOP
                RewardType.Skin => Skin.GetDefinition(rewardData.SkinType, rewardData.SkinId),
#endif
                RewardType.INFINITE_LIVE => Rewards.infiniteLive,
                RewardType.NO_ADS => Rewards.noAds,
                _ => null
            };
        }
    }
}