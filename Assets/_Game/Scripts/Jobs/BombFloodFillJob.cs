using Unity.Burst;
using Unity.Collections;
using Unity.Jobs;
using Unity.Mathematics;
using UnityEngine;

namespace OP.BlockSand.Bomb
{
    /// <summary>
    /// Job to perform flood fill algorithm from bomb target position to find all connected pixels of same material
    /// </summary>
    [BurstCompile]
    public struct BombFloodFillJob : IJob
    {
        // Input data
        [ReadOnly] public int worldWidth;
        [ReadOnly] public int worldHeight;
        [ReadOnly] public NativeArray<Pixel> pixelBuffer;
        [ReadOnly] public NativeArray<int2> seedPixels; // Starting pixels from bomb radius
        [ReadOnly] public int targetMaterialIndex; // Material index to flood fill
        
        // Output data
        public NativeArray<int2> affectedPixels; // All pixels that will be affected by bomb
        public NativeArray<int> affectedPixelCount; // Number of affected pixels
        
        public void Execute()
        {
            // Initialize
            var visited = new NativeArray<bool>(pixelBuffer.Length, Allocator.Temp);
            var stack = new NativeArray<int2>(pixelBuffer.Length, Allocator.Temp);
            var tempAffectedPixels = new NativeList<int2>(Allocator.Temp);
            
            // Process each seed pixel
            for (var seedIndex = 0; seedIndex < seedPixels.Length; seedIndex++)
            {
                var seedPixel = seedPixels[seedIndex];
                var x = seedPixel.x;
                var y = seedPixel.y;
                
                if (!IsValidCoordinate(x, y)) continue;
                
                var index = GetIndex(x, y);
                var pixel = pixelBuffer[index];
                
                // Skip if empty, wrong material, or already visited
                if (pixel.IsEmpty() || pixel.materialIndex != targetMaterialIndex || visited[index])
                    continue;
                
                // Perform flood fill from this seed
                FloodFillFromSeed(x, y, visited, stack, tempAffectedPixels);
            }
            
            // Copy results to output array
            var count = math.min(tempAffectedPixels.Length, affectedPixels.Length);
            affectedPixelCount[0] = count;
            
            for (var i = 0; i < count; i++)
            {
                affectedPixels[i] = tempAffectedPixels[i];
            }
            
            // Debug output
            // UnityEngine.Debug.Log($"BombFloodFillJob: Found {count} affected pixels for material {targetMaterialIndex}");
            
            // Cleanup
            visited.Dispose();
            stack.Dispose();
            tempAffectedPixels.Dispose();
        }
        
        private void FloodFillFromSeed(int startX, int startY, NativeArray<bool> visited,
            NativeArray<int2> stack, NativeList<int2> affectedPixels)
        {
            var stackTop = 0;
            var targetMaterial = pixelBuffer[GetIndex(startX, startY)].materialIndex;
            
            // Add starting pixel to stack
            stack[stackTop] = new int2(startX, startY);
            stackTop++;
            
            while (stackTop > 0)
            {
                // Pop from stack
                stackTop--;
                var current = stack[stackTop];
                var x = current.x;
                var y = current.y;
                var index = GetIndex(x, y);
                
                // Skip if already visited, empty, or different material
                if (visited[index] || pixelBuffer[index].IsEmpty() ||
                    pixelBuffer[index].materialIndex != targetMaterial)
                    continue;
                
                // Mark as visited and add to affected pixels
                visited[index] = true;
                affectedPixels.Add(new int2(x, y));
                
                // Add neighbors to stack (4-directional for more controlled spread)
                AddNeighborToStack(x - 1, y, stack, ref stackTop);     // Left
                AddNeighborToStack(x + 1, y, stack, ref stackTop);     // Right
                AddNeighborToStack(x, y - 1, stack, ref stackTop);     // Down
                AddNeighborToStack(x, y + 1, stack, ref stackTop);     // Up
                
                // Optionally add diagonal neighbors for 8-directional spread
                AddNeighborToStack(x - 1, y - 1, stack, ref stackTop); // Bottom-left
                AddNeighborToStack(x + 1, y - 1, stack, ref stackTop); // Bottom-right
                AddNeighborToStack(x - 1, y + 1, stack, ref stackTop); // Top-left
                AddNeighborToStack(x + 1, y + 1, stack, ref stackTop); // Top-right
            }
        }
        
        private void AddNeighborToStack(int x, int y, NativeArray<int2> stack, ref int stackTop)
        {
            if (!IsValidCoordinate(x, y) || stackTop >= stack.Length) return;
            
            stack[stackTop] = new int2(x, y);
            stackTop++;
        }
        
        private bool IsValidCoordinate(int x, int y)
        {
            return x >= 0 && x < worldWidth && y >= 0 && y < worldHeight;
        }
        
        private int GetIndex(int x, int y)
        {
            return y * worldWidth + x;
        }
    }
    
    /// <summary>
    /// Data structure to hold bomb flood fill job data
    /// </summary>
    public struct BombFloodFillJobData
    {
        public NativeArray<Pixel> pixelBufferCopy;
        public NativeArray<int2> seedPixels;
        public NativeArray<int2> affectedPixels;
        public NativeArray<int> affectedPixelCount;
        
        public bool IsValid => pixelBufferCopy.IsCreated && seedPixels.IsCreated && 
                              affectedPixels.IsCreated && affectedPixelCount.IsCreated;
        
        public static BombFloodFillJobData Create(int maxPixels, int seedCount)
        {
            return new BombFloodFillJobData
            {
                seedPixels = new NativeArray<int2>(seedCount, Allocator.Persistent),
                affectedPixels = new NativeArray<int2>(maxPixels, Allocator.Persistent),
                affectedPixelCount = new NativeArray<int>(1, Allocator.Persistent)
            };
        }
        
        public void Dispose()
        {
            if (pixelBufferCopy.IsCreated) pixelBufferCopy.Dispose();
            if (seedPixels.IsCreated) seedPixels.Dispose();
            if (affectedPixels.IsCreated) affectedPixels.Dispose();
            if (affectedPixelCount.IsCreated) affectedPixelCount.Dispose();
        }
    }
}
