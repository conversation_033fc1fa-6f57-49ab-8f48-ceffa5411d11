using System;
using UnityEngine;

namespace OP.BlockSand.Shape
{
    /// <summary>
    /// Interface for objects that can be dragged and dropped on the board
    /// </summary>
    public interface IDraggableShape
    {
        /// <summary>
        /// Unique identifier for this draggable object
        /// </summary>
        string Id { get; }
        
        /// <summary>
        /// Transform component of this draggable object
        /// </summary>
        Transform OTransform { get; }
        
        /// <summary>
        /// Bounding rectangle of this draggable object
        /// </summary>
        Rect Rect { get; }
        
        /// <summary>
        /// Show the draggable object with animation
        /// </summary>
        void Show();
        
        /// <summary>
        /// Handle when the object is selected for dragging
        /// </summary>
        /// <param name="worldPos">World position where selection occurred</param>
        /// <param name="boardRect">Board boundaries</param>
        void HandleSelected(Vector3 worldPos, Rect boardRect);
        
        /// <summary>
        /// Handle when the object is being moved
        /// </summary>
        /// <param name="worldPos">Current world position</param>
        /// <param name="boardRect">Board boundaries</param>
        void HandleMoving(Vector3 worldPos, Rect boardRect);
        
        /// <summary>
        /// Handle when the object is released
        /// </summary>
        void HandleRelease();
        
        /// <summary>
        /// Move the object to a target position
        /// </summary>
        /// <param name="targetPos">Target world position</param>
        /// <param name="onComplete">Callback when movement is complete</param>
        void MoveToTarget(Vector3 targetPos, Action onComplete);
        
        /// <summary>
        /// Move the object back to its original position
        /// </summary>
        void MoveBack();
        
        /// <summary>
        /// Reset the object state and clean up resources
        /// </summary>
        void Reset();
    }
}
