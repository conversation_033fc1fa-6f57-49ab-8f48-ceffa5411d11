Tôi muốn implement thêm tính năng booster Bomb. 
- Khi user bấm sử dụng booster ở UIPanelGame, GameManager sẽ hiển thị và control 1 icon target hiển thị trong board. Dựa vào vị trí target này, ta sẽ tính được các pixel trong phạm vi cho trước. Icon target cũng cần di chuyển trong phạm vi của board như ShapeController vậy. User có thể di chuyển icon target bằng tay. 
- Dựa vào vị trí target, ta lấy được các pixel trong phạm vi ảnh hưởng. Từ danh sách các pixel đó và materialIndex, chúng ta sẽ sử dụng flood fill để lan tới tất cả các pixel cùng materialIndex. Mỗi khi user thay đổi vị trí target rồi release, ta mới tính lại danh sách pixel ảnh hưởng
- Trong quá trình xác định vị trí target, ta sẽ hiển thị các pixel ảnh hưởng để user có thể thấy được phạm vi ảnh hưởng của booster. Các pixel này sẽ được setup lại màu để tạo hiệu ứng sóng lượn từ điểm xuất phát. Bạn có thể sử dụng PixelWorld.DrawPixelForAnimation() để vẽ màu tương ứng. Mỗi pixel sẽ set màu theo thứ tự: Màu hiện tại nhưng trắng hơn, màu trắng, trắng có alpha 0.5f, trắng có alpha 0f. Sau khi sóng lượn hết tất cả các pixel, chúng ta chạy lặp lại.
- Trong UIPanelGame có button _buttonUseBomb, khi user tương tác, GameManager sẽ hiển thị effect nổ tại vị trí target, Board sẽ clear toàn bộ pixel ảnh hưởng. Clear cũng theo animation sóng lượn lan ra dần
- Có thể cân nhắc sử dụng Job cho các tính toán flood fill pixel ảnh hưởng. Tuy nhiên khi ấy cần chia state cho Board, sang state UsingBomb chẳng hạn