using System.Collections.Generic;
using System.Linq;
using Cysharp.Threading.Tasks;
using OnePuz.Data;
using OnePuz.Extensions;
using Sirenix.OdinInspector;
using UnityEngine;

namespace OP.BlockSand.Shape
{
    public struct OnRequestSpawningShapesEvent
    {
    }

    public class ShapeSpawner : MonoBehaviour
    {
        [SerializeField]
        private ShapeDefinition _shapeDefinition;

        [SerializeField]
        private PixelMaterials _materialDefinition;

        [SerializeField]
        private Transform _shapeContainer;

        [SerializeField]
        private Transform[] _spawnPoints;

        private readonly ShapeController[] _shapes = new ShapeController[3];

        private SpawningRule _spawningRule;
        private Vector2Int _boardSize;
        private bool _hasFinishedAnalysis = false;
        
        public bool JustUnlockedNewColor { get; set; }
        public BlockType NewUnlockedColor { get; set; }

        public void Init(Vector2Int boardSize)
        {
            _boardSize = boardSize;

            var spawningRuleConfigs = DataShortcut.ShapeSpawner.ruleConfigs;
            #if UNITY_EDITOR
            spawningRuleConfigs = Core.Definition.ShapeSpawningRule.ruleConfigs;
            #endif
            _spawningRule = new SpawningRule(spawningRuleConfigs, _materialDefinition, _boardSize.x);
            var currentLevel = DataShortcut.Score.level;
            _spawningRule.SetLevel(currentLevel);

            LoadSavedShapes(DataShortcut.Board.savedShapes);

            // Subscribe to score level up events to update spawn rules
            this.EventSubscribe<OnScoreLevelUpEvent>(HandleScoreLevelUp);
            this.EventSubscribe<OnMapAnalysisJobFinishedEvent>(HandleMapAnalysisFinished);
            this.EventSubscribe<OnComboChangedEvent>(HandleOnComboChanged);
            _hasFinishedAnalysis = false;
        }

        public void LoadSavedShapes(List<BoardData.Shape> savedShapes)
        {
            if (savedShapes == null || savedShapes.Count == 0)
            {
                SpawnShapesAsync(showImmediately: true).Forget();
                return;
            }

            for (var i = 0; i < savedShapes.Count; i++)
            {
                if (savedShapes[i] == null) continue;
                _shapes[i] = SpawnShape(i, _shapeDefinition.shapes.First(t => t.id == savedShapes[i].id), savedShapes[i].blockTypes);
                _shapes[i].Show();
            }
        }

        [Button]
        public async UniTask SpawnShapesAsync(bool showImmediately = false)
        {
            // OLogger.LogNotice($"[Start] === Shape Spawn Debug === ");
            _hasFinishedAnalysis = false;
            var startTime = Time.time;
            Core.Event.Fire(new OnRequestSpawningShapesEvent());
            await UniTask.WaitUntil(() => _hasFinishedAnalysis || Time.time - startTime > 0.5f);

            _spawningRule.OnSpawnAllShapes();

            // OLogger.LogNotice($"[Finish] === Shape Spawn Debug === " +
            //                   $"Score Level: {DataShortcut.Score.level} " +
            //                   $"Current Score: {DataShortcut.Score.currentScore} " +
            //                   $"Spawn Rule: {_spawnRule.GetDebugInfo()}\n" +
            //                   $"Time: {Time.time:F1}s");
            for (var i = 0; i < _shapes.Length; i++)
            {
                var shape = _shapes[i];
                if (!shape) continue;

                Despawn(shape);
                _shapes[i] = null;
            }

            // Use batch rule-based spawning for better special shape positioning
            var batchResults = _spawningRule.GenerateBatchShapeBlockTypes(_shapeDefinition.defaultBlockCount, _spawnPoints.Length);

            for (var i = 0; i < _spawnPoints.Length && i < batchResults.Count; i++)
            {
                var spawnResult = batchResults[i];
                var shapeDatum = _shapeDefinition.GetRandomShape(spawnResult.isMultiColor);

                _shapes[i] = SpawnShape(i, shapeDatum, spawnResult.blockTypes);
                if (showImmediately)
                {
                    _shapes[i].Show();
                }

                // Log spawn info for debugging
                // OLogger.Log($"Spawned shape {i}: {spawnResult.debugInfo}");
            }
        }

        private ShapeController SpawnShape(int index, ShapeDefinition.Datum datum, List<BlockType> blockTypes)
        {
            var shape = Core.ScenePool.Spawn(_shapeDefinition.shapePrefab.gameObject, _shapeContainer).GetComponent<ShapeController>();
            shape.OTransform.localPosition = _spawnPoints[index].localPosition;
            shape.Init(datum, blockTypes);
            return shape;
        }
        
        public void SpawnAllShapesWithType(BlockType blockType)
        {
            for (var i = 0; i < _shapes.Length; i++)
            {
                var shape = _shapes[i];
                if (!shape) continue;

                Despawn(shape);
                _shapes[i] = null;
            }

            var blockTypes = new List<BlockType> { blockType, blockType, blockType, blockType };
            
            for (var i = 0; i < _spawnPoints.Length; i++)
            {
                var shapeDatum = _shapeDefinition.GetRandomShape();

                _shapes[i] = SpawnShape(i, shapeDatum, blockTypes);
                _shapes[i].Show();
            }
        }

        public void ShowAllShapes()
        {
            foreach (var shape in _shapes)
            {
                if (!shape) continue;
                shape.Show();
            }
        }
        
        private void HideAllShapes()
        {
            foreach (var shape in _shapes)
            {
                if (!shape) continue;
                shape.Hide();
            }
        }

        public ShapeController GetShape(int index)
        {
            return _shapes[index];
        }

        public bool AnyShapesLeft()
        {
            return _shapes.Any(t => t);
        }

        public void RemoveShape(int index)
        {
            if (_shapes[index])
            {
                _shapes[index] = null;
            }
        }
        
        public void MoveBackAllShapes()
        {
            foreach (var shape in _shapes)
            {
                if (!shape) continue;
                shape.MoveBack();
            }
        }

        public void Despawn(ShapeController shape)
        {
            shape.Reset();
            Core.ScenePool.Recycle(shape.gameObject);
        }

        public List<BoardData.Shape> GetSavedData()
        {
            return _shapes.Select(t => t == null ? null : new BoardData.Shape() { id = t.Id, blockTypes = t.BlockTypes }).ToList();
        }

        public void Reset()
        {
            foreach (var shape in _shapes)
            {
                if (!shape) continue;
                Despawn(shape);
            }

            // Reset the spawn rule system
            _spawningRule.Reset();
        }

        private void HandleScoreLevelUp(OnScoreLevelUpEvent e)
        {
            _spawningRule.SetLevel(e.level);
            
            JustUnlockedNewColor = false;
            
            var lastLevelConfig = _spawningRule.GetLevelConfig(e.level - 1);
            var currentLevelConfig = _spawningRule.GetLevelConfig(e.level);
            if (lastLevelConfig == null || currentLevelConfig == null) return;
            if (currentLevelConfig.colorCount <= lastLevelConfig.colorCount) return;
            JustUnlockedNewColor = true;
            NewUnlockedColor = currentLevelConfig.availableBlockTypes.Last();
        }

        private void HandleMapAnalysisFinished(OnMapAnalysisJobFinishedEvent obj)
        {
            if (!obj.analysisResults.IsValid)
            {
                _hasFinishedAnalysis = true;
                return;
            }
            
            _spawningRule.UpdateMapAnalysis(obj.analysisResults);
            _hasFinishedAnalysis = true;
        }
        
        private void HandleOnComboChanged(OnComboChangedEvent e)
        {
            _spawningRule.SetCurrentCombo(e.currentCombo);
        }

        private void OnDisable()
        {
            this.EventUnsubscribe<OnScoreLevelUpEvent>(HandleScoreLevelUp);
            this.EventUnsubscribe<OnMapAnalysisJobFinishedEvent>(HandleMapAnalysisFinished);
            this.EventUnsubscribe<OnComboChangedEvent>(HandleOnComboChanged);

            _spawningRule?.Reset();
        }

#if UNITY_EDITOR
        [Button("Show Spawn Debug Info")]
        private void ShowDebugInfo()
        {
            var debugInfo = _spawningRule.GetDebugInfo();
            Debug.Log($"Shape Spawn Debug: {debugInfo}");
        }
#endif
    }
}