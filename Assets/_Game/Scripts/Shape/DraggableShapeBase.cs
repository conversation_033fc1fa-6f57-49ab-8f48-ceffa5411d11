using System;
using PrimeTween;
using UnityEngine;

namespace OP.BlockSand.Shape
{
    /// <summary>
    /// Base class for draggable objects with common drag-drop functionality
    /// </summary>
    public abstract class DraggableShapeBase : MonoBehaviour, IDraggableShape
    {
        [SerializeField]
        protected ShapeDefinition _shapeDefinition;
        
        protected Vector3 _originalPosition;
        private Vector3 _offsetFromOriginalPosition;
        private Vector3 _selectedWorldPosition;
        
        private bool _handMoving;
        private bool _shouldMove;
        private bool _handReleased;
        private bool _shouldMoveBack;
        private bool _shouldBePlaced;
        private Vector3 _lastTargetPosition;
        private Vector3 _targetPosition;
        
        protected Action _onPlacedShape;
        
        private Transform _transform;
        public Transform OTransform => _transform ??= transform;
        
        public abstract string Id { get; protected set; }
        public abstract Rect Rect { get; }
        
        protected virtual void LateUpdate()
        {
            if (!_shouldMove)
                return;
            
            var speed = 200f;
            if (_handReleased)
            {
                if (_shouldBePlaced)
                    speed = 6f;
                else if (_shouldMoveBack)
                    speed = 60f;
                else
                    speed = 3f;
            }
            else if (!_handMoving)
                speed = 60f;
            
            var delta = speed * Time.deltaTime;
            var sqrDistance = Vector3.SqrMagnitude(_targetPosition - OTransform.position);
            
            OTransform.position = Vector3.MoveTowards(OTransform.position, _targetPosition, delta);
            
            if (sqrDistance <= 0.4f && _shouldMoveBack)
            {
                OTransform.position = _targetPosition;
                _shouldMove = false;
                _shouldMoveBack = false;
            }
            
            if (sqrDistance <= 0.05f && _shouldBePlaced)
            {
                _shouldBePlaced = false;
                OnPlacementComplete();
            }
        }
        
        public virtual void Show()
        {
            // re-update _originalPosition, cause parent position may change
            _originalPosition = transform.position;
            
            AnimateScale(0.8f, 0.2f);
        }
        
        public virtual void Hide()
        {
            AnimateScale(0, 0.25f);
        }
        
        public virtual void HandleSelected(Vector3 worldPos, Rect boardRect)
        {
            _shouldMoveBack = false;
            _handMoving = false;
            _handReleased = false;
            _shouldBePlaced = false;
            
            _selectedWorldPosition = worldPos;
            _offsetFromOriginalPosition = worldPos - _originalPosition;
            
            _targetPosition = worldPos - _offsetFromOriginalPosition;
            _targetPosition.y += _shapeDefinition.moveUpOffsetCurve.Evaluate((_targetPosition.y - _originalPosition.y) / _shapeDefinition.moveUpDistance);
            _targetPosition = ClampPosition(_targetPosition, boardRect);
            _lastTargetPosition = _targetPosition;
            
            _shouldMove = true;
            
            AnimateScale(1, 0.15f);
            OnSelected();
        }
        
        public virtual void HandleMoving(Vector3 worldPos, Rect boardRect)
        {
            var changeOffset = worldPos - _selectedWorldPosition;
            _targetPosition = worldPos - _offsetFromOriginalPosition;
            var additionalHorizontal = (changeOffset.x >= 0 ? 1f : -1f) * _shapeDefinition.moveHorizontalOffsetCurve.Evaluate(Mathf.Abs(changeOffset.x) / _shapeDefinition.moveHorizontalDistance);
            _targetPosition.x += additionalHorizontal;
            _targetPosition.y += _shapeDefinition.moveUpOffsetCurve.Evaluate(changeOffset.y / _shapeDefinition.moveUpDistance);
            _targetPosition = ClampPosition(_targetPosition, boardRect);
            
            if (Vector3.Distance(_targetPosition, _lastTargetPosition) < 0.2f && !_handMoving)
            {
                _shouldMove = true;
            }
            else
            {
                if (!_handMoving)
                    AnimateScale(1, 0.05f);
                
                _handMoving = true;
                _shouldMove = true;
            }
        }
        
        protected virtual Vector3 ClampPosition(Vector3 targetWorldPosition, Rect boardRect)
        {
            var clampedPosition = targetWorldPosition;
            clampedPosition.x = Mathf.Clamp(clampedPosition.x, boardRect.xMin + Rect.width / 2, boardRect.xMax - Rect.width / 2);
            clampedPosition.y = Mathf.Clamp(clampedPosition.y, float.MinValue, boardRect.yMax - Rect.height / 2);
            
            return clampedPosition;
        }
        
        public virtual void HandleRelease()
        {
            _handReleased = true;
            OnReleased();
        }
        
        public virtual void MoveToTarget(Vector3 targetPos, Action onComplete)
        {
            _targetPosition = targetPos;
            _shouldBePlaced = true;
            _onPlacedShape = onComplete;
        }
        
        public virtual void MoveBack()
        {
            _shouldMoveBack = true;
            _targetPosition = _originalPosition;
            
            AnimateScale(0.8f, 0.2f);
        }
        
        protected virtual void AnimateScale(float blockScale, float duration, Action onComplete = null)
        {
            if (Mathf.Approximately(OTransform.localScale.x, blockScale))
            {
                onComplete?.Invoke();
                return;
            }
            
            Tween.Scale(OTransform, blockScale, duration).OnComplete(() => onComplete?.Invoke());
        }
        
        public virtual void Reset()
        {
            _shouldMove = false;
            _shouldMoveBack = false;
        }
        
        // Abstract methods for subclasses to implement
        protected abstract void OnSelected();
        protected abstract void OnReleased();
        protected abstract void OnPlacementComplete();
    }
}
