using System;
using System.Collections.Generic;
using PrimeTween;
using Sirenix.OdinInspector;
using UnityEngine;

namespace OP.BlockSand.Shape
{
    public class ShapeController : DraggableShapeBase
    {
        public struct BlockPixel
        {
            public Vector2Int coordinate;
            public BlockType blockType;
            public Color32 color;
        }

        private const int BlockNormalSortOrder = 5;
        private const int BlockSelectedSortOrder = 10;

        [SerializeField]
        private GameObject _elementPrefab;

        [SerializeField]
        private BlockDefinitions _blockDefinitions;

        private readonly List<Vector2Int> _coordinates = new();
        private readonly List<BlockElement> _elements = new();

        [SerializeField, ReadOnly]
        private Rect _rect;

        public override Rect Rect => _rect;

        // All pixels, coordinates from bottom left
        private readonly List<BlockPixel> _pixels = new();

        public override string Id { get; protected set; }
        public List<BlockType> BlockTypes { get; private set; }

        public void Init(ShapeDefinition.Datum datum, List<BlockType> blockTypes)
        {
            Id = datum.id;
            BlockTypes = blockTypes;

            _coordinates.Clear();
            _coordinates.AddRange(datum.coordinates);
            _rect = CalculateRect();
            _originalPosition = transform.position;

            _elements.Clear();

            for (var i = 0; i < _coordinates.Count; i++)
            {
                var coordinate = _coordinates[i];
                var blockType = blockTypes[i];
                var element = Core.ScenePool.Spawn(_elementPrefab, transform).GetComponent<BlockElement>();
                element.Init(blockType, coordinate, _blockDefinitions.GetRandomTexture(blockType));
                element.UpdateSortOrder(BlockNormalSortOrder);
                element.transform.localPosition = new Vector2(coordinate.x - datum.size.x / 2f + 0.5f, coordinate.y - datum.size.y / 2f + 0.5f) * _blockDefinitions.blockSize;

                _elements.Add(element);
            }

            UpdatePixels();

            transform.localScale = Vector3.zero;
        }

        public override void MoveToTarget(Vector3 targetPos, Action onComplete)
        {
            _onPlacedShape = onComplete;
            base.MoveToTarget(targetPos, onComplete);
        }

        private void UpdateBlockSortOrder(int value)
        {
            foreach (var element in _elements)
            {
                element.UpdateSortOrder(value);
            }
        }

        private void UpdatePixels()
        {
            _pixels.Clear();

            foreach (var element in _elements)
            {
                var elementPixels = element.GetPixels();
                for (var i = 0; i < elementPixels.Length; i++)
                {
                    var xInt = i % BlockElement.PixelNumberSize + element.Coordinate.x * BlockElement.PixelNumberSize;
                    var yInt = i / BlockElement.PixelNumberSize + element.Coordinate.y * BlockElement.PixelNumberSize;
                    _pixels.Add(new BlockPixel()
                    {
                        coordinate = new Vector2Int(xInt, yInt),
                        blockType = element.BlockType,
                        color = elementPixels[i]
                    });
                }
            }
        }

        public List<BlockPixel> GetPixels() => _pixels;

        public int GetPixelCount() => _pixels.Count;

        private Rect CalculateRect()
        {
            var minX = float.MaxValue;
            var maxX = float.MinValue;
            var minY = float.MaxValue;
            var maxY = float.MinValue;

            var blockSize = _blockDefinitions.blockSize;
            var halfBlockSize = blockSize / 2f;
            foreach (var coordinate in _coordinates)
            {
                var position = (Vector2)coordinate * blockSize;
                minX = Mathf.Min(minX, position.x - halfBlockSize);
                maxX = Mathf.Max(maxX, position.x + halfBlockSize);
                minY = Mathf.Min(minY, position.y - halfBlockSize);
                maxY = Mathf.Max(maxY, position.y + halfBlockSize);
            }

            return new Rect(minX, minY, maxX - minX, maxY - minY);
        }

        public override void Reset()
        {
            base.Reset();

            foreach (var element in _elements)
            {
                Core.ScenePool.Recycle(element.gameObject);
            }
        }

        // Abstract method implementations for DraggableShapeBase
        protected override void OnSelected()
        {
            UpdateBlockSortOrder(BlockSelectedSortOrder);
        }

        protected override void OnReleased()
        {
            UpdateBlockSortOrder(BlockNormalSortOrder);
        }

        protected override void OnPlacementComplete()
        {
            _onPlacedShape?.Invoke();
        }
    }
}