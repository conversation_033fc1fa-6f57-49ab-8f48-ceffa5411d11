using System;
using UnityEngine;

namespace OP.BlockSand
{
    public enum FingerTouchState
    {
        Pressed,
        Hold,
        Released
    }

    public struct OnShapeSelectedEvent
    {
        public int shapeIndex;
        public Vector2 worldPosition;
    }

    public struct OnShapeMovedEvent
    {
        public int shapeIndex;
        public Vector2 worldPosition;
    }

    public struct OnShapeReleasedEvent
    {
        public int shapeIndex;
        public Vector2 worldPosition;
        public bool hasCanceled;
    }

    public class PlayerInput : MonoBehaviour
    {
        private Camera _camera;

        private bool _hasSelected = false;
        private int _selectedIndex = -1;
        private string _selectedObjectTag = "";

        private bool _isInitialized = false;
        private bool _canTouch = false;
        private int _currentFingerId;
        private FingerTouchState _currentTouchState = FingerTouchState.Released;

        public void Init(Camera mainCamera)
        {
            _camera = mainCamera;

            Input.multiTouchEnabled = false;
            EnableTouch(true);

            _isInitialized = true;
        }

        private void Update()
        {
            if (!_isInitialized)
                return;

            if (!_canTouch) return;

            if (Application.isEditor)
            {
                Vector2 mousePos = Input.mousePosition;
                if (Input.GetMouseButtonDown(0))
                {
                    HandlePressed(mousePos);
                }

                if (Input.GetMouseButton(0))
                {
                    HandleMove(mousePos);
                }

                if (Input.GetMouseButtonUp(0))
                {
                    HandleRelease(mousePos);
                }
            }
            else
            {
                var matchedFinger = false;
                if (Input.touchCount == 1)
                {
                    if (_currentTouchState == FingerTouchState.Released)
                        _currentFingerId = Input.GetTouch(0).fingerId;

                    var touch = Input.GetTouch(0);
                    if (_currentFingerId == touch.fingerId)
                    {
                        matchedFinger = true;
                        var touchPos = touch.position;
                        switch (touch.phase)
                        {
                            case TouchPhase.Began:
                                _currentTouchState = FingerTouchState.Pressed;
                                HandlePressed(touchPos);
                                break;
                            case TouchPhase.Moved:
                            case TouchPhase.Stationary:
                                _currentTouchState = FingerTouchState.Hold;
                                HandleMove(touchPos);
                                break;
                            case TouchPhase.Ended:
                            case TouchPhase.Canceled:
                                _currentTouchState = FingerTouchState.Released;
                                HandleRelease(touchPos);
                                break;
                            default:
                                throw new ArgumentOutOfRangeException();
                        }
                    }
                }

                if ((matchedFinger && Input.touchCount != 0 && Input.touchCount <= 1) || _currentTouchState == FingerTouchState.Released) return;
                _currentTouchState = FingerTouchState.Released;
                _hasSelected = false;
                _selectedObjectTag = "";

                if (_selectedIndex >= 0)
                    Core.Event.Fire(new OnShapeReleasedEvent
                    {
                        shapeIndex = _selectedIndex,
                        worldPosition = Vector3.zero,
                        hasCanceled = true
                    });
            }
        }

        private void HandlePressed(Vector2 touchPos)
        {
            if (IsPointerOverUI())
                return;
            
            var worldPos = _camera.ScreenToWorldPoint(touchPos);
            var hit = Physics2D.Raycast(worldPos, Vector2.zero);

            if (hit.collider)
            {
                if (hit.collider.CompareTag("Selection Area 1"))
                {
                    _hasSelected = true;
                    _selectedIndex = 0;
                    Core.Event.Fire(new OnShapeSelectedEvent
                    {
                        shapeIndex = _selectedIndex,
                        worldPosition = worldPos
                    });
                }
                else if (hit.collider.CompareTag("Selection Area 2"))
                {
                    _hasSelected = true;
                    _selectedIndex = 1;
                    Core.Event.Fire(new OnShapeSelectedEvent
                    {
                        shapeIndex = _selectedIndex,
                        worldPosition = worldPos
                    });
                }
                else if (hit.collider.CompareTag("Selection Area 3"))
                {
                    _hasSelected = true;
                    _selectedIndex = 2;
                    Core.Event.Fire(new OnShapeSelectedEvent
                    {
                        shapeIndex = _selectedIndex,
                        worldPosition = worldPos
                    });
                }
                else
                {
                    _selectedObjectTag = hit.collider.tag;
                }
            }
            else
            {
                // Spawn touch fx
            }
        }

        private void HandleMove(Vector2 touchPos)
        {
            Vector2 worldPos = _camera.ScreenToWorldPoint(touchPos);

            if (_hasSelected)
            {
                Core.Event.Fire(new OnShapeMovedEvent
                {
                    shapeIndex = _selectedIndex,
                    worldPosition = worldPos
                });
            }
        }

        private void HandleRelease(Vector2 touchPos)
        {
            Vector2 worldPos = _camera.ScreenToWorldPoint(touchPos);

            if (_hasSelected)
            {
                Core.Event.Fire(new OnShapeReleasedEvent
                {
                    shapeIndex = _selectedIndex,
                    worldPosition = worldPos,
                    hasCanceled = false
                });

                _hasSelected = false;
            }
            else if (!string.IsNullOrEmpty(_selectedObjectTag))
            {
                var hit = Physics2D.Raycast(worldPos, Vector2.zero);

                if (hit.collider && string.CompareOrdinal(_selectedObjectTag, hit.collider.tag) == 0)
                {
                }
            }

            _selectedObjectTag = "";
        }

        public void EnableTouch(bool enabled)
        {
            _canTouch = enabled;
        }

        private bool IsPointerOverUI()
        {
#if UNITY_EDITOR
            return Core.UI.pEventSystem.IsPointerOverGameObject();
#else
            return Core.UI.pEventSystem.IsPointerOverGameObject(0);
#endif
        }
    }
}