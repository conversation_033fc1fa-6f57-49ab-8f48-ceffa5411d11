using System;
using System.Threading;
using Cysharp.Threading.Tasks;
using OnePuz.Data;
using OnePuz.Definition;
using OnePuz.Extensions;
using OnePuz.Services;
using UnityEngine;

namespace OnePuz.Live
{
    public struct EventLiveChanged
    {
    }

    public class LiveService : ILiveService, IServiceLoad
    {
        private LiveData _liveData;
        private LiveDefinition _definition;

        private CancellationTokenSource _countDownRefillCTS;
        private CancellationTokenSource _countDownInfiniteLiveCTS;

        public TimeSpan TimeLeftToRefill { get; private set; }
        public TimeSpan TimeLeftToInfiniteLive { get; private set; }
        public bool IsInfinite => _liveData.IsInfinite;
        public bool IsRefilling => _liveData.IsRefilling;
        public int CurrentLive => _liveData.CurrentLive;
        public bool CanPlay => _liveData.CurrentLive > 0 || _liveData.IsInfinite;

        public void Load()
        {
            _liveData = DataShortcut.Live;
            _definition = Core.Definition.Live;

            _countDownRefillCTS = new CancellationTokenSource();
            _countDownInfiniteLiveCTS = new CancellationTokenSource();

            TimeLeftToRefill = _liveData.RefillEndTime - DateTime.Now;
            if (_liveData.IsRefilling)
            {
                _countDownRefillCTS = _countDownRefillCTS.RefreshToken();
                CountDownRefillAsync(_countDownRefillCTS.Token).Forget();
            }

            TimeLeftToInfiniteLive = _liveData.InfiniteLiveEndTime - DateTime.Now;
            if (_liveData.IsInfinite)
            {
                _countDownInfiniteLiveCTS = _countDownInfiniteLiveCTS.RefreshToken();
                CountDownInfiniteLiveAsync(_countDownInfiniteLiveCTS.Token).Forget();
            }
        }

        public void EarnInfiniteLive(long durationInSeconds)
        {
            _liveData.EarnInfiniteLive(durationInSeconds);
            _liveData.SetLives(_definition.maxLive);

            Core.Event.Fire(new EventLiveChanged());

            if (_countDownRefillCTS != null && !_countDownRefillCTS.IsCancellationRequested)
                _countDownRefillCTS.Cancel();

            _countDownInfiniteLiveCTS = _countDownInfiniteLiveCTS.RefreshToken();
            CountDownInfiniteLiveAsync(_countDownInfiniteLiveCTS.Token).Forget();
        }

        public void EarnLive(int amount = 1)
        {
            var currentLives = _liveData.CurrentLive;
            currentLives = Mathf.Clamp(currentLives + amount, 0, _definition.maxLive);
            _liveData.SetLives(currentLives);
        }

        public void LostLive()
        {
            if (_liveData.IsInfinite) return;
            var currentLives = Math.Max(0, CurrentLive - 1);
            _liveData.SetLives(currentLives);

            if (_liveData.IsRefilling) return;

            if (CurrentLive >= _definition.maxLive)
                return;

            _liveData.Refill(_definition.refillLiveDuration);

            _countDownRefillCTS = _countDownRefillCTS.RefreshToken();
            CountDownRefillAsync(_countDownRefillCTS.Token).Forget();
        }

        private async UniTask CountDownRefillAsync(CancellationToken cancellationToken)
        {
            while (!cancellationToken.IsCancellationRequested && _liveData.IsRefilling)
            {
                TimeLeftToRefill = _liveData.RefillEndTime - DateTime.Now;

                if (TimeLeftToRefill.TotalSeconds <= 0)
                {
                    EarnLive();
                    
                    _liveData.Refill(_definition.refillLiveDuration);
                    
                    if (CurrentLive >= _definition.maxLive)
                        _liveData.FinishRefill();
                }

                Core.Event.Fire(new EventLiveChanged());

                await UniTask.Delay(TimeSpan.FromSeconds(1), cancellationToken: cancellationToken);
            }
        }

        private async UniTask CountDownInfiniteLiveAsync(CancellationToken cancellationToken)
        {
            while (!cancellationToken.IsCancellationRequested && _liveData.IsInfinite)
            {
                TimeLeftToInfiniteLive = _liveData.InfiniteLiveEndTime - DateTime.Now;

                if (TimeLeftToInfiniteLive.TotalSeconds <= 0)
                {
                    _liveData.FinishInfiniteLive();
                }

                Core.Event.Fire(new EventLiveChanged());

                await UniTask.Delay(TimeSpan.FromSeconds(1), cancellationToken: cancellationToken);
            }
        }
    }
}