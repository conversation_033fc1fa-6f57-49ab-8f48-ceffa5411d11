using OnePuz.Attributes;
using OnePuz.Data;
using UnityEngine;

public class MapViewUI : MonoBehaviour
{
    [Serial<PERSON>Field, PreAssigned("ItemContainer")]
    private Transform itemContainer;

    [SerializeField, PreAssigned("Item_0")]
    private MapItemUI mapItemUIPrefab;

    public void InitMapView()
    {
        mapItemUIPrefab.gameObject.SetActive(false);
        var levelInit = DataShortcut.Level.Current;
        for (var i = 0; i < 10; i++)
        {
            var mapItem = Instantiate(mapItemUIPrefab, itemContainer);
            mapItem.InitMapItemUI(levelInit, i);
            mapItem.gameObject.SetActive(true);
            levelInit++;
        }
    }
}