{"dependencies": {"com.coffee.softmask-for-ugui": "https://github.com/mob-sakai/SoftMaskForUGUI.git?path=Packages/src", "com.coffee.ui-effect": "https://github.com/mob-sakai/UIEffect.git?path=Packages/src", "com.coffee.ui-particle": "https://github.com/mob-sakai/ParticleEffectForUGUI.git", "com.cysharp.unitask": "https://github.com/Cysharp/UniTask.git?path=src/UniTask/Assets/Plugins/UniTask", "com.google.external-dependency-manager": "1.2.183", "com.google.firebase.analytics": "file:../GooglePackages/com.google.firebase.analytics-12.0.0.tgz", "com.google.firebase.app": "file:../GooglePackages/com.google.firebase.app-12.0.0.tgz", "com.google.firebase.crashlytics": "file:../GooglePackages/com.google.firebase.crashlytics-12.0.0.tgz", "com.google.firebase.remote-config": "file:../GooglePackages/com.google.firebase.remote-config-12.0.0.tgz", "com.kyrylokuzyk.primetween": "file:../Assets/Plugins/PrimeTween/internal/com.kyrylokuzyk.primetween.tgz", "com.unity.addressables": "2.2.2", "com.unity.collab-proxy": "2.7.1", "com.unity.editorcoroutines": "1.0.0", "com.unity.feature.2d": "2.0.1", "com.unity.ide.rider": "3.0.36", "com.unity.ide.visualstudio": "2.0.22", "com.unity.mobile.android-logcat": "1.4.5", "com.unity.purchasing": "4.12.2", "com.unity.recorder": "5.1.2", "com.unity.render-pipelines.universal": "file:../Packages/com.unity.render-pipelines.universal", "com.unity.splines": "2.7.2", "com.unity.test-framework": "1.4.5", "com.unity.ugui": "2.0.0", "com.unity.visualscripting": "1.9.5", "com.unity.modules.accessibility": "1.0.0", "com.unity.modules.ai": "1.0.0", "com.unity.modules.androidjni": "1.0.0", "com.unity.modules.animation": "1.0.0", "com.unity.modules.assetbundle": "1.0.0", "com.unity.modules.audio": "1.0.0", "com.unity.modules.director": "1.0.0", "com.unity.modules.imageconversion": "1.0.0", "com.unity.modules.imgui": "1.0.0", "com.unity.modules.jsonserialize": "1.0.0", "com.unity.modules.particlesystem": "1.0.0", "com.unity.modules.physics": "1.0.0", "com.unity.modules.physics2d": "1.0.0", "com.unity.modules.screencapture": "1.0.0", "com.unity.modules.terrainphysics": "1.0.0", "com.unity.modules.tilemap": "1.0.0", "com.unity.modules.ui": "1.0.0", "com.unity.modules.uielements": "1.0.0", "com.unity.modules.umbra": "1.0.0", "com.unity.modules.unityanalytics": "1.0.0", "com.unity.modules.unitywebrequest": "1.0.0", "com.unity.modules.unitywebrequestassetbundle": "1.0.0", "com.unity.modules.unitywebrequestaudio": "1.0.0", "com.unity.modules.unitywebrequesttexture": "1.0.0", "com.unity.modules.unitywebrequestwww": "1.0.0"}, "scopedRegistries": [{"name": "package.openupm.com", "url": "https://package.openupm.com", "scopes": ["com.google.external-dependency-manager", "com.google"]}]}